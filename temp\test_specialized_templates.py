#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试专用表结构模板功能
验证4类工资表专用模板的创建和字段映射生成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.system_config.specialized_table_templates import SpecializedTableTemplates
from src.modules.data_import.specialized_field_mapping_generator import SpecializedFieldMappingGenerator
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def test_specialized_templates():
    """测试专用模板功能"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("专用表结构模板功能测试")
    print("=" * 80)
    
    # 1. 测试模板管理器
    print("\n1. 测试专用模板管理器")
    print("-" * 50)
    
    templates = SpecializedTableTemplates()
    
    # 获取模板信息
    template_info = templates.get_template_info()
    for template_key, info in template_info.items():
        print(f"模板: {template_key}")
        print(f"  字段数量: {info['field_count']}")
        print(f"  描述: {info['description']}")
        print(f"  关键字段: {info['key_fields']}")
        print()
    
    # 2. 测试表头检测
    print("\n2. 测试表头检测功能")
    print("-" * 50)
    
    test_headers = {
        "离休人员工资表": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "护理费", "离休补贴"],
        "退休人员工资表": ["序号", "人员代码", "姓名", "基本退休费", "2016待遇调整", "增资预付"],
        "全部在职人员工资表": ["序号", "工号", "姓名", "2025年岗位工资", "2025年薪级工资", "基础性绩效"],
        "A岗职工工资表": ["序号", "工号", "姓名", "2025年校龄工资", "2025年生活补贴"]
    }
    
    for sheet_name, headers in test_headers.items():
        detected_template = templates.detect_template_by_headers(headers)
        print(f"{sheet_name}: 检测到模板 -> {detected_template}")
    
    # 3. 测试字段映射生成
    print("\n3. 测试字段映射生成")
    print("-" * 50)
    
    mapping_generator = SpecializedFieldMappingGenerator()
    
    for sheet_name, headers in test_headers.items():
        template_key = templates.detect_template_by_headers(headers)
        mapping = mapping_generator.generate_mapping(headers, template_key)
        
        print(f"\n{sheet_name} (模板: {template_key}):")
        print(f"  生成映射数量: {len(mapping)}")
        
        # 显示前5个映射
        for i, (db_field, display_name) in enumerate(mapping.items()):
            if i < 5:
                print(f"    {db_field} -> {display_name}")
        if len(mapping) > 5:
            print(f"    ... 还有 {len(mapping) - 5} 个映射")
    
    # 4. 测试专用表创建
    print("\n4. 测试专用表创建")
    print("-" * 50)
    
    try:
        table_manager = DynamicTableManager()
        
        # 测试创建一个专用表
        test_template = "active_employees"
        success = table_manager.create_specialized_salary_table(
            template_key=test_template,
            month="05",
            year=2027,
            employee_type="test_active_employees"
        )
        
        if success:
            print(f"✓ 专用表创建成功: {test_template}")
            
            # 检查表结构
            table_name = "salary_data_2027_05_test_active_employees"
            if table_manager.table_exists(table_name):
                columns = table_manager.get_table_columns(table_name)
                print(f"  表字段数量: {len(columns)}")
                print(f"  前5个字段: {[col.get('name', '') for col in columns[:5]]}")
            else:
                print("✗ 表创建后未找到")
        else:
            print(f"✗ 专用表创建失败: {test_template}")
            
    except Exception as e:
        print(f"✗ 专用表创建测试失败: {e}")
    
    # 5. 对比通用模板和专用模板
    print("\n5. 通用模板 vs 专用模板对比")
    print("-" * 50)
    
    try:
        # 通用模板字段
        generic_template = table_manager._table_templates.get("salary_data")
        if generic_template:
            generic_fields = [col.name for col in generic_template.columns]
            print(f"通用模板字段数: {len(generic_fields)}")
            print(f"通用模板字段: {generic_fields}")
        
        # 专用模板字段
        active_template = templates.get_template("active_employees")
        if active_template:
            active_fields = [field.name for field in active_template]
            print(f"\n在职人员专用模板字段数: {len(active_fields)}")
            print(f"专用模板字段: {active_fields}")
            
            # 找出差异
            generic_set = set(generic_fields) if generic_template else set()
            active_set = set(active_fields)
            
            only_in_generic = generic_set - active_set
            only_in_active = active_set - generic_set
            
            print(f"\n只在通用模板中的字段: {list(only_in_generic)}")
            print(f"只在专用模板中的字段: {list(only_in_active)}")
            
    except Exception as e:
        print(f"✗ 模板对比失败: {e}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    test_specialized_templates()
