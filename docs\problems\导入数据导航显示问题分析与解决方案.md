# 导入数据导航显示问题分析与解决方案

## 问题描述

用户导入2027年9月数据后，出现以下问题：
1. 导入提示创建了4张表，但左侧导航只显示3个导航项
2. 右侧表格中有些表显示中文表头，有些显示英文表头
3. 有一个表完全看不到

## 问题分析

### 1. 导航显示不全的根本原因

通过日志分析发现：
- 实际创建了4个表：
  - `salary_data_2027_09` (离休人员，2行)
  - `salary_data_2027_09_pension_employees` (退休人员，13行)
  - `salary_data_2027_09_active_employees` (全部在职人员，1396行)
  - `salary_data_2027_09_a_grade_employees` (A岗职工，62行)

- 但在导航树生成时，`salary_data_2027_09`被跳过了

**根本原因**：`_parse_table_name`方法的解析逻辑有缺陷
```python
def _parse_table_name(self, table_name: str) -> Optional[Tuple[int, int, str]]:
    parts = table_name.split('_')
    if len(parts) >= 5 and parts[0] == 'salary' and parts[1] == 'data':
        # 期望格式：salary_data_YYYY_MM_employee_type
        year = int(parts[2])
        month = int(parts[3])
        employee_type = '_'.join(parts[4:])
        return year, month, employee_type
    else:
        return None  # salary_data_2027_09 只有4个部分，被跳过
```

### 2. 表头显示不一致的原因

字段映射配置不完整：
- `salary_data_2027_09_active_employees`: 有完整的字段映射 → 显示中文表头
- `salary_data_2027_09_a_grade_employees`: 有完整的字段映射 → 显示中文表头  
- `salary_data_2027_09_pension_employees`: 字段映射保存失败 → 显示英文表头
- `salary_data_2027_09`: 没有字段映射 → 显示英文表头

从日志可以看到：
```
ERROR | 保存配置文件失败: [WinError 5] 拒绝访问。: 'state/data/field_mappings.json.tmp' -> 'state/data/field_mappings.json'
```

## 解决方案

### 方案1：修复表名解析逻辑（推荐）

修改`_parse_table_name`方法，支持两种表名格式：
1. `salary_data_YYYY_MM_employee_type` (专用表)
2. `salary_data_YYYY_MM` (通用表，默认为离休人员)

### 方案2：修复字段映射保存问题

解决文件权限问题，确保所有表的字段映射都能正确保存。

### 方案3：统一表命名规范

将`salary_data_2027_09`重命名为`salary_data_2027_09_retired_employees`，保持命名一致性。

## 实施计划

### 第一步：修复表名解析逻辑
```python
def _parse_table_name(self, table_name: str) -> Optional[Tuple[int, int, str]]:
    try:
        parts = table_name.split('_')
        if len(parts) >= 4 and parts[0] == 'salary' and parts[1] == 'data':
            year = int(parts[2])
            month = int(parts[3])
            
            if len(parts) >= 5:
                # 专用表格式：salary_data_YYYY_MM_employee_type
                employee_type = '_'.join(parts[4:])
            else:
                # 通用表格式：salary_data_YYYY_MM (默认为离休人员)
                employee_type = 'retired_employees'
                
            return year, month, employee_type
        else:
            return None
    except (ValueError, IndexError):
        return None
```

### 第二步：修复字段映射保存问题
检查并修复`state/data/field_mappings.json`的文件权限问题。

### 第三步：补充缺失的字段映射
为没有字段映射的表手动添加映射配置。

## 预期效果

修复后应该能看到：
- 左侧导航显示4个完整的导航项：离休人员、退休人员、全部在职人员、A岗职工
- 所有表格都显示中文表头
- 数据能正常加载和显示

## 流程图

```mermaid
graph TD
    A[导入Excel数据] --> B[创建4个数据表]
    B --> C[生成导航树数据]
    C --> D{解析表名}
    D -->|成功| E[添加到导航树]
    D -->|失败| F[跳过该表]
    E --> G[显示在左侧导航]
    F --> H[导航项缺失]
    
    B --> I[生成字段映射]
    I --> J{保存映射配置}
    J -->|成功| K[显示中文表头]
    J -->|失败| L[显示英文表头]
```

## 时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant I as 导入模块
    participant D as 数据库管理器
    participant N as 导航面板
    participant T as 表格显示
    
    U->>I: 导入Excel文件
    I->>D: 创建4个数据表
    I->>I: 生成字段映射
    I-->>I: 保存映射失败(权限问题)
    I->>N: 触发导航刷新
    N->>D: 获取表列表
    D->>D: 解析表名
    D-->>D: salary_data_2027_09解析失败
    D->>N: 返回3个有效表
    N->>U: 显示3个导航项
    U->>T: 点击查看表格
    T->>T: 应用字段映射
    T-->>T: 部分表无映射配置
    T->>U: 显示混合表头(中英文)
```
