#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第二阶段：导入逻辑修改
验证MultiSheetImporter的专用模板集成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from pathlib import Path
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def test_phase2_import_logic():
    """测试第二阶段导入逻辑修改"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("第二阶段：导入逻辑修改测试")
    print("=" * 80)
    
    # 1. 初始化组件
    print("\n1. 初始化组件")
    print("-" * 50)
    
    try:
        table_manager = DynamicTableManager()
        importer = MultiSheetImporter(table_manager)
        print("✓ MultiSheetImporter初始化成功")
        print(f"✓ 专用模板管理器已集成: {hasattr(importer, 'specialized_templates')}")
        print(f"✓ 专用映射生成器已集成: {hasattr(importer, 'specialized_mapping_generator')}")
    except Exception as e:
        print(f"✗ 组件初始化失败: {e}")
        return
    
    # 2. 测试Sheet名称模板检测
    print("\n2. 测试Sheet名称模板检测")
    print("-" * 50)
    
    test_sheet_names = [
        "离休人员工资表",
        "退休人员工资表", 
        "全部在职人员工资表",
        "A岗职工工资表",
        "其他工资表"
    ]
    
    for sheet_name in test_sheet_names:
        template_key = importer._detect_template_from_sheet_name(sheet_name)
        print(f"{sheet_name}: {template_key}")
    
    # 3. 测试表名生成逻辑
    print("\n3. 测试表名生成逻辑")
    print("-" * 50)
    
    year, month = 2027, 5
    for sheet_name in test_sheet_names:
        table_name = importer._generate_consistent_table_name(sheet_name, year, month)
        print(f"{sheet_name}: {table_name}")
    
    # 4. 测试专用模板检测（使用模拟数据）
    print("\n4. 测试专用模板检测")
    print("-" * 50)
    
    test_headers = {
        "离休人员工资表": ["序号", "人员代码", "姓名", "基本离休费", "护理费"],
        "退休人员工资表": ["序号", "人员代码", "姓名", "基本退休费", "2016待遇调整"],
        "全部在职人员工资表": ["序号", "工号", "姓名", "2025年岗位工资", "2025年薪级工资"],
        "A岗职工工资表": ["序号", "工号", "姓名", "2025年校龄工资", "2025年生活补贴"]
    }
    
    for sheet_name, headers in test_headers.items():
        template_key = importer.specialized_templates.detect_template_by_headers(headers)
        print(f"{sheet_name}: 检测到模板 {template_key}")
    
    # 5. 测试专用字段映射生成
    print("\n5. 测试专用字段映射生成")
    print("-" * 50)
    
    for sheet_name, headers in test_headers.items():
        template_key = importer.specialized_templates.detect_template_by_headers(headers)
        mapping = importer.specialized_mapping_generator.generate_mapping(headers, template_key)
        print(f"{sheet_name} (模板: {template_key}): {len(mapping)} 个字段映射")
        
        # 显示前3个映射
        for i, (db_field, display_name) in enumerate(mapping.items()):
            if i < 3:
                print(f"  {db_field} -> {display_name}")
        if len(mapping) > 3:
            print(f"  ... 还有 {len(mapping) - 3} 个映射")
    
    # 6. 测试专用表创建逻辑
    print("\n6. 测试专用表创建逻辑")
    print("-" * 50)
    
    # 创建模拟DataFrame
    test_data = {
        "序号": [1, 2, 3],
        "工号": ["19990089", "20161565", "20191782"],
        "姓名": ["张三", "李四", "王五"],
        "2025年岗位工资": [5000.0, 5500.0, 6000.0],
        "2025年薪级工资": [3000.0, 3200.0, 3500.0],
        "应发工资": [8000.0, 8700.0, 9500.0]
    }
    
    test_df = pd.DataFrame(test_data)
    print(f"✓ 创建测试数据: {len(test_df)} 行, {len(test_df.columns)} 列")
    
    # 模拟专用表创建流程
    excel_headers = list(test_df.columns)
    template_key = importer.specialized_templates.detect_template_by_headers(excel_headers)
    print(f"✓ 检测到模板: {template_key}")
    
    if template_key != "salary_data":
        employee_type_mapping = {
            "retired_employees": "retired_employees",
            "pension_employees": "pension_employees", 
            "active_employees": "active_employees",
            "a_grade_employees": "a_grade_employees"
        }
        employee_type = employee_type_mapping.get(template_key, "unknown")
        table_name = f"salary_data_{year}_{month:02d}_{employee_type}_test"
        
        # 尝试创建专用表
        try:
            create_success = table_manager.create_specialized_salary_table(
                template_key=template_key,
                month=f"{month:02d}",
                year=year,
                employee_type=f"{employee_type}_test"
            )
            
            if create_success:
                print(f"✓ 专用表创建成功: {table_name}")
                
                # 检查表结构
                if table_manager.table_exists(table_name):
                    columns = table_manager.get_table_columns(table_name)
                    print(f"  表字段数量: {len(columns)}")
                    print(f"  前5个字段: {[col.get('name', '') for col in columns[:5]]}")
                else:
                    print("✗ 表创建后未找到")
            else:
                print(f"✗ 专用表创建失败: {table_name}")
                
        except Exception as e:
            print(f"✗ 专用表创建异常: {e}")
    
    # 7. 测试字段映射配置保存
    print("\n7. 测试字段映射配置保存")
    print("-" * 50)
    
    try:
        # 生成字段映射
        mapping = importer.specialized_mapping_generator.generate_mapping(excel_headers, template_key)
        
        # 保存映射配置
        mapping_data = {
            "field_mappings": mapping,
            "original_excel_headers": dict(zip(mapping.keys(), excel_headers)),
            "metadata": {
                "source": "phase2_test",
                "template_key": template_key,
                "auto_generated": True,
                "test_mode": True
            }
        }
        
        test_table_name = f"salary_data_{year}_{month:02d}_test_mapping"
        success = importer.config_sync_manager.save_complete_mapping(test_table_name, mapping_data)
        
        if success:
            print(f"✓ 字段映射配置保存成功: {test_table_name}")
            print(f"  映射字段数量: {len(mapping)}")
        else:
            print(f"✗ 字段映射配置保存失败")
            
    except Exception as e:
        print(f"✗ 字段映射配置保存异常: {e}")
    
    print("\n测试完成!")
    print("\n总结:")
    print("✓ MultiSheetImporter已成功集成专用模板")
    print("✓ 专用模板检测功能正常")
    print("✓ 专用表创建逻辑已实现")
    print("✓ 专用字段映射生成功能正常")
    print("✓ 第二阶段导入逻辑修改完成")

if __name__ == "__main__":
    test_phase2_import_logic()
