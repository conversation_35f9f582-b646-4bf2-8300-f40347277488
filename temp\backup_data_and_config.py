#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段：备份现有数据和配置
在进行数据迁移前，备份所有重要数据和配置文件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import shutil
import sqlite3
import json
from datetime import datetime
from pathlib import Path
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def backup_data_and_config():
    """备份现有数据和配置"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("第三阶段：备份现有数据和配置")
    print("=" * 80)
    
    # 创建备份目录
    backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path(f"backup/phase3_migration_{backup_timestamp}")
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"备份目录: {backup_dir}")
    
    backup_results = {
        "timestamp": backup_timestamp,
        "backup_dir": str(backup_dir),
        "items": {}
    }
    
    # 1. 备份数据库文件
    print("\n1. 备份数据库文件")
    print("-" * 50)
    
    try:
        db_path = Path("data/db/salary_system.db")
        if db_path.exists():
            backup_db_path = backup_dir / "salary_system.db"
            shutil.copy2(db_path, backup_db_path)
            
            # 获取数据库信息
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 获取每个表的记录数
            table_info = {}
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_info[table] = count
            
            conn.close()
            
            backup_results["items"]["database"] = {
                "status": "success",
                "source": str(db_path),
                "backup": str(backup_db_path),
                "size": backup_db_path.stat().st_size,
                "tables": table_info
            }
            
            print(f"✓ 数据库备份成功: {backup_db_path}")
            print(f"  数据库大小: {backup_db_path.stat().st_size} 字节")
            print(f"  表数量: {len(tables)}")
            for table, count in table_info.items():
                if table.startswith("salary_data"):
                    print(f"    {table}: {count} 条记录")
        else:
            print("✗ 数据库文件不存在")
            backup_results["items"]["database"] = {"status": "not_found"}
            
    except Exception as e:
        print(f"✗ 数据库备份失败: {e}")
        backup_results["items"]["database"] = {"status": "error", "error": str(e)}
    
    # 2. 备份配置文件
    print("\n2. 备份配置文件")
    print("-" * 50)
    
    config_files = [
        "config.json",
        "state/data/field_mappings.json",
        "state/data/import_history.json"
    ]
    
    for config_file in config_files:
        try:
            config_path = Path(config_file)
            if config_path.exists():
                backup_config_path = backup_dir / config_path.name
                shutil.copy2(config_path, backup_config_path)
                
                backup_results["items"][config_path.name] = {
                    "status": "success",
                    "source": str(config_path),
                    "backup": str(backup_config_path),
                    "size": backup_config_path.stat().st_size
                }
                
                print(f"✓ 配置文件备份成功: {config_file}")
            else:
                print(f"⚠ 配置文件不存在: {config_file}")
                backup_results["items"][config_path.name] = {"status": "not_found"}
                
        except Exception as e:
            print(f"✗ 配置文件备份失败 {config_file}: {e}")
            backup_results["items"][config_path.name] = {"status": "error", "error": str(e)}
    
    # 3. 备份日志文件
    print("\n3. 备份日志文件")
    print("-" * 50)
    
    try:
        logs_dir = Path("logs")
        if logs_dir.exists():
            backup_logs_dir = backup_dir / "logs"
            shutil.copytree(logs_dir, backup_logs_dir)
            
            # 统计日志文件
            log_files = list(backup_logs_dir.glob("*.log"))
            total_size = sum(f.stat().st_size for f in log_files)
            
            backup_results["items"]["logs"] = {
                "status": "success",
                "source": str(logs_dir),
                "backup": str(backup_logs_dir),
                "files": len(log_files),
                "total_size": total_size
            }
            
            print(f"✓ 日志文件备份成功: {len(log_files)} 个文件")
            print(f"  总大小: {total_size} 字节")
        else:
            print("⚠ 日志目录不存在")
            backup_results["items"]["logs"] = {"status": "not_found"}
            
    except Exception as e:
        print(f"✗ 日志文件备份失败: {e}")
        backup_results["items"]["logs"] = {"status": "error", "error": str(e)}
    
    # 4. 导出当前表结构信息
    print("\n4. 导出当前表结构信息")
    print("-" * 50)
    
    try:
        table_manager = DynamicTableManager()
        
        # 获取所有工资表
        salary_tables = []
        db_path = Path("data/db/salary_system.db")
        if db_path.exists():
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data_%'")
            salary_tables = [row[0] for row in cursor.fetchall()]
            conn.close()
        
        table_structures = {}
        for table_name in salary_tables:
            try:
                columns = table_manager.get_table_columns(table_name)
                table_structures[table_name] = {
                    "columns": columns,
                    "field_count": len(columns),
                    "field_names": [col.get('name', '') for col in columns]
                }
            except Exception as e:
                table_structures[table_name] = {"error": str(e)}
        
        # 保存表结构信息
        structure_file = backup_dir / "table_structures.json"
        with open(structure_file, 'w', encoding='utf-8') as f:
            json.dump(table_structures, f, ensure_ascii=False, indent=2)
        
        backup_results["items"]["table_structures"] = {
            "status": "success",
            "backup": str(structure_file),
            "tables": len(table_structures)
        }
        
        print(f"✓ 表结构信息导出成功: {len(table_structures)} 个表")
        for table_name, info in table_structures.items():
            if "field_count" in info:
                print(f"    {table_name}: {info['field_count']} 个字段")
        
    except Exception as e:
        print(f"✗ 表结构信息导出失败: {e}")
        backup_results["items"]["table_structures"] = {"status": "error", "error": str(e)}
    
    # 5. 保存备份报告
    print("\n5. 保存备份报告")
    print("-" * 50)
    
    try:
        report_file = backup_dir / "backup_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(backup_results, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 备份报告保存成功: {report_file}")
        
        # 显示备份摘要
        print(f"\n备份摘要:")
        print(f"  备份时间: {backup_timestamp}")
        print(f"  备份目录: {backup_dir}")
        
        success_count = sum(1 for item in backup_results["items"].values() 
                          if item.get("status") == "success")
        total_count = len(backup_results["items"])
        print(f"  成功项目: {success_count}/{total_count}")
        
        return str(backup_dir)
        
    except Exception as e:
        print(f"✗ 备份报告保存失败: {e}")
        return None

if __name__ == "__main__":
    backup_dir = backup_data_and_config()
    if backup_dir:
        print(f"\n✅ 备份完成! 备份目录: {backup_dir}")
    else:
        print(f"\n❌ 备份失败!")
