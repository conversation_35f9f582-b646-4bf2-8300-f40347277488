#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断字段映射显示问题的脚本
分析为什么主界面显示英文字段名而不是中文字段名
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def diagnose_field_mapping_issue():
    """诊断字段映射显示问题"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("字段映射显示问题诊断报告")
    print("=" * 80)
    
    # 1. 检查字段映射配置文件
    print("\n1. 检查字段映射配置文件")
    print("-" * 40)
    
    config_sync = ConfigSyncManager()
    
    # 检查配置文件是否存在
    config_file = "state/data/field_mappings.json"
    if os.path.exists(config_file):
        print(f"✓ 配置文件存在: {config_file}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        print(f"✓ 配置版本: {config_data.get('version', 'unknown')}")
        print(f"✓ 表映射数量: {len(config_data.get('table_mappings', {}))}")
        
        # 列出所有表
        for table_name in config_data.get('table_mappings', {}):
            print(f"  - {table_name}")
    else:
        print(f"✗ 配置文件不存在: {config_file}")
        return
    
    # 2. 检查具体表的字段映射
    print("\n2. 检查具体表的字段映射")
    print("-" * 40)
    
    test_table = "salary_data_2027_05_active_employees"
    mapping = config_sync.load_mapping(test_table)
    
    if mapping:
        print(f"✓ 表 {test_table} 的字段映射:")
        for db_field, display_name in mapping.items():
            print(f"  {db_field} → {display_name}")
    else:
        print(f"✗ 表 {test_table} 没有字段映射")
    
    # 3. 检查数据库表结构
    print("\n3. 检查数据库表结构")
    print("-" * 40)
    
    try:
        table_manager = DynamicTableManager()
        
        if table_manager.table_exists(test_table):
            print(f"✓ 数据库表存在: {test_table}")
            
            # 获取表结构
            columns = table_manager.get_table_columns(test_table)
            print(f"✓ 数据库字段数量: {len(columns)}")
            
            print("数据库字段列表:")
            for col in columns[:10]:  # 只显示前10个字段
                print(f"  - {col.get('name', 'unknown')}")
            if len(columns) > 10:
                print(f"  ... 还有 {len(columns) - 10} 个字段")
                
        else:
            print(f"✗ 数据库表不存在: {test_table}")
            
    except Exception as e:
        print(f"✗ 检查数据库表失败: {e}")
    
    # 4. 模拟字段映射应用过程
    print("\n4. 模拟字段映射应用过程")
    print("-" * 40)
    
    try:
        import pandas as pd
        
        # 创建模拟数据框
        sample_data = {
            'id': [1, 2, 3],
            'employee_id': ['19990089', '20161565', '20191782'],
            'employee_name': ['张三', '胡四平', '肖强'],
            'id_card': ['None', 'None', 'None'],
            'department': ['None', 'None', 'None'],
            'position': ['None', 'None', 'None'],
            'basic_salary': ['None', 'None', 'None'],
            'performance_bonus': ['None', 'None', 'None'],
            'overtime_pay': [102.0, 0.0, 0.0]
        }
        
        df = pd.DataFrame(sample_data)
        print(f"✓ 创建模拟数据框: {len(df)} 行, {len(df.columns)} 列")
        print(f"原始列名: {list(df.columns)}")
        
        # 应用字段映射
        if mapping:
            column_rename_map = {}
            for db_field, display_name in mapping.items():
                if db_field in df.columns and display_name:
                    column_rename_map[db_field] = display_name
            
            print(f"✓ 可应用的映射: {len(column_rename_map)} 个")
            for db_field, display_name in column_rename_map.items():
                print(f"  {db_field} → {display_name}")
            
            # 应用重命名
            if column_rename_map:
                df_renamed = df.rename(columns=column_rename_map)
                print(f"✓ 映射后列名: {list(df_renamed.columns)}")
            else:
                print("✗ 没有可应用的映射")
        else:
            print("✗ 没有字段映射配置")
    
    except Exception as e:
        print(f"✗ 模拟字段映射失败: {e}")
    
    # 5. 分析问题原因
    print("\n5. 问题原因分析")
    print("-" * 40)
    
    print("根据诊断结果，可能的问题原因:")
    print("1. 字段映射配置存在，但在主界面数据加载时没有正确应用")
    print("2. 表名不匹配：配置中的表名与实际加载的表名不一致")
    print("3. 字段映射应用的时机不对：在数据显示之前没有应用映射")
    print("4. 字段映射的键值对应关系错误：数据库字段名与配置中的键不匹配")
    
    # 6. 建议解决方案
    print("\n6. 建议解决方案")
    print("-" * 40)
    
    print("建议按以下步骤解决:")
    print("1. 检查主界面数据加载时的表名是否与配置文件中的表名一致")
    print("2. 确认 _apply_field_mapping_to_dataframe 方法是否被正确调用")
    print("3. 在数据显示前添加调试日志，确认字段映射是否被应用")
    print("4. 检查 ConfigSyncManager.load_mapping() 方法是否返回正确的映射")
    
    print("\n诊断完成!")

if __name__ == "__main__":
    diagnose_field_mapping_issue()
