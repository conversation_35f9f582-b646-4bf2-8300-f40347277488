#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试导航显示修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager

def test_table_name_parsing():
    """测试表名解析功能"""
    print("=== 测试表名解析功能 ===")
    
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    # 测试不同格式的表名
    test_tables = [
        "salary_data_2027_09",  # 通用表格式
        "salary_data_2027_09_pension_employees",  # 专用表格式
        "salary_data_2027_09_active_employees",
        "salary_data_2027_09_a_grade_employees",
        "invalid_table_name"  # 无效格式
    ]
    
    for table_name in test_tables:
        result = table_manager._parse_table_name(table_name)
        if result:
            year, month, employee_type = result
            print(f"✅ {table_name}: 年份={year}, 月份={month}, 类型={employee_type}")
        else:
            print(f"❌ {table_name}: 解析失败")

def test_navigation_tree_data():
    """测试导航树数据生成"""
    print("\n=== 测试导航树数据生成 ===")
    
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    # 获取导航树数据
    navigation_data = table_manager.get_navigation_tree_data()
    
    if navigation_data:
        print(f"📊 导航树包含 {len(navigation_data)} 个年份")
        
        # 检查2027年9月的数据
        if 2027 in navigation_data and 9 in navigation_data[2027]:
            items = navigation_data[2027][9]
            print(f"🎯 2027年9月包含 {len(items)} 个导航项:")
            for item in items:
                print(f"  - {item['display_name']} ({item['table_name']})")
        else:
            print("❌ 2027年9月数据未找到")
    else:
        print("❌ 导航树数据为空")

def test_field_mappings():
    """测试字段映射配置"""
    print("\n=== 测试字段映射配置 ===")
    
    config_sync = ConfigSyncManager()
    
    # 测试2027年9月的所有表
    test_tables = [
        "salary_data_2027_09",
        "salary_data_2027_09_pension_employees", 
        "salary_data_2027_09_active_employees",
        "salary_data_2027_09_a_grade_employees"
    ]
    
    for table_name in test_tables:
        mapping = config_sync.load_mapping(table_name)
        if mapping:
            print(f"✅ {table_name}: 有字段映射 ({len(mapping)} 个字段)")
        else:
            print(f"❌ {table_name}: 无字段映射")

def test_table_list():
    """测试表列表获取"""
    print("\n=== 测试表列表获取 ===")
    
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    # 获取所有salary_data表
    tables = table_manager.get_table_list(table_type='salary_data')
    
    print(f"📋 找到 {len(tables)} 个工资数据表")
    
    # 筛选2027年9月的表
    sept_2027_tables = [t for t in tables if '2027_09' in t.get('table_name', '')]
    print(f"🎯 2027年9月相关表: {len(sept_2027_tables)} 个")
    
    for table in sept_2027_tables:
        table_name = table.get('table_name', 'Unknown')
        display_name = table.get('display_name', 'Unknown')
        year = table.get('year', 'Unknown')
        month = table.get('month', 'Unknown')
        print(f"  - {table_name}: {display_name} (年份={year}, 月份={month})")

def main():
    """主测试函数"""
    print("🔧 开始测试导航显示修复效果...\n")
    
    try:
        test_table_name_parsing()
        test_navigation_tree_data()
        test_field_mappings()
        test_table_list()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()
