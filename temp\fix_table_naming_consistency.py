#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复表命名一致性问题
将 salary_data_2027_09 重命名为 salary_data_2027_09_retired_employees
"""

import sys
import os
import sqlite3
import json
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

def backup_table_data(db_path: str, table_name: str) -> bool:
    """备份表数据"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            print(f"❌ 表 {table_name} 不存在")
            return False
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # 获取数据
        cursor.execute(f"SELECT * FROM {table_name}")
        data = cursor.fetchall()
        
        # 保存备份
        backup_data = {
            "table_name": table_name,
            "columns": columns,
            "data": data,
            "row_count": len(data)
        }
        
        backup_file = f"temp/backup_{table_name}.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
        
        conn.close()
        print(f"✅ 表 {table_name} 备份完成: {len(data)} 行数据")
        return True
        
    except Exception as e:
        print(f"❌ 备份表 {table_name} 失败: {e}")
        return False

def rename_table_in_database(db_path: str, old_name: str, new_name: str) -> bool:
    """在数据库中重命名表"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查新表名是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (new_name,))
        if cursor.fetchone():
            print(f"❌ 新表名 {new_name} 已存在")
            conn.close()
            return False
        
        # 重命名表
        cursor.execute(f"ALTER TABLE {old_name} RENAME TO {new_name}")
        
        # 更新元数据表
        cursor.execute("UPDATE table_metadata SET table_name=? WHERE table_name=?", (new_name, old_name))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 表重命名成功: {old_name} -> {new_name}")
        return True
        
    except Exception as e:
        print(f"❌ 重命名表失败: {e}")
        return False

def update_field_mappings(old_name: str, new_name: str) -> bool:
    """更新字段映射配置"""
    try:
        config_file = "state/data/field_mappings.json"
        
        if not Path(config_file).exists():
            print(f"❌ 配置文件不存在: {config_file}")
            return False
        
        # 读取配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查旧配置是否存在
        if old_name not in config.get("table_mappings", {}):
            print(f"❌ 旧表 {old_name} 的字段映射不存在")
            return False
        
        # 移动配置
        table_mappings = config["table_mappings"]
        table_mappings[new_name] = table_mappings.pop(old_name)
        
        # 更新元数据
        if "metadata" in table_mappings[new_name]:
            table_mappings[new_name]["metadata"]["table_name"] = new_name
            table_mappings[new_name]["metadata"]["last_modified"] = "2025-06-27T20:15:00.000000"
            table_mappings[new_name]["metadata"]["renamed_from"] = old_name
        
        config["last_updated"] = "2025-06-27T20:15:00.000000"
        
        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 字段映射更新成功: {old_name} -> {new_name}")
        return True
        
    except Exception as e:
        print(f"❌ 更新字段映射失败: {e}")
        return False

def verify_rename_result(db_path: str, new_name: str) -> bool:
    """验证重命名结果"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查新表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (new_name,))
        if not cursor.fetchone():
            print(f"❌ 新表 {new_name} 不存在")
            return False
        
        # 检查数据完整性
        cursor.execute(f"SELECT COUNT(*) FROM {new_name}")
        row_count = cursor.fetchone()[0]
        
        # 检查元数据
        cursor.execute("SELECT * FROM table_metadata WHERE table_name=?", (new_name,))
        metadata = cursor.fetchone()
        
        conn.close()
        
        if metadata:
            print(f"✅ 验证成功: 表 {new_name} 存在，包含 {row_count} 行数据")
            return True
        else:
            print(f"❌ 元数据验证失败: 表 {new_name} 的元数据不存在")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复表命名一致性问题...\n")
    
    old_table_name = "salary_data_2027_09"
    new_table_name = "salary_data_2027_09_retired_employees"
    db_path = "data/db/salary_system.db"
    
    try:
        # 1. 备份原表数据
        print("1. 备份原表数据...")
        if not backup_table_data(db_path, old_table_name):
            return
        
        # 2. 重命名数据库表
        print("\n2. 重命名数据库表...")
        if not rename_table_in_database(db_path, old_table_name, new_table_name):
            return
        
        # 3. 更新字段映射配置
        print("\n3. 更新字段映射配置...")
        if not update_field_mappings(old_table_name, new_table_name):
            return
        
        # 4. 验证重命名结果
        print("\n4. 验证重命名结果...")
        if not verify_rename_result(db_path, new_table_name):
            return
        
        print(f"\n✅ 表命名一致性修复完成!")
        print(f"   旧表名: {old_table_name}")
        print(f"   新表名: {new_table_name}")
        print(f"   现在所有表都使用统一的命名格式: salary_data_YYYY_MM_employee_type")
        
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {e}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()
