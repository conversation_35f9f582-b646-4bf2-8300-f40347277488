#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复字段映射显示问题的脚本
根据实际数据库表结构更新字段映射配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from datetime import datetime
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def fix_field_mapping_display_issue():
    """修复字段映射显示问题"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("修复字段映射显示问题")
    print("=" * 80)
    
    config_sync = ConfigSyncManager()
    table_manager = DynamicTableManager()
    
    # 需要修复的表列表
    tables_to_fix = [
        "salary_data_2027_05_active_employees",
        "salary_data_2027_05_a_grade_employees", 
        "salary_data_2027_05_pension_employees",
        "salary_data_2027_05_retired_employees"
    ]
    
    # 通用字段的中文映射
    common_field_mappings = {
        "id": "ID",
        "employee_id": "工号",
        "employee_name": "姓名", 
        "id_card": "身份证号",
        "department": "部门名称",
        "position": "职位",
        "basic_salary": "基本工资",
        "performance_bonus": "绩效奖金",
        "overtime_pay": "加班费",
        "allowance": "津贴",
        "deduction": "扣款",
        "total_salary": "应发工资",
        "month": "月份",
        "year": "年份",
        "created_at": "创建时间",
        "updated_at": "更新时间"
    }
    
    for table_name in tables_to_fix:
        print(f"\n处理表: {table_name}")
        print("-" * 50)
        
        try:
            # 检查表是否存在
            if not table_manager.table_exists(table_name):
                print(f"✗ 表不存在，跳过: {table_name}")
                continue
            
            # 获取实际数据库字段
            columns = table_manager.get_table_columns(table_name)
            db_fields = [col.get('name', '') for col in columns if col.get('name')]
            
            print(f"✓ 数据库字段数量: {len(db_fields)}")
            print(f"数据库字段: {db_fields}")
            
            # 创建新的字段映射
            new_mapping = {}
            
            for field in db_fields:
                if field in common_field_mappings:
                    new_mapping[field] = common_field_mappings[field]
                else:
                    # 对于未知字段，保持原名或生成友好名称
                    friendly_name = field.replace('_', ' ').title()
                    new_mapping[field] = friendly_name
            
            print(f"✓ 生成新映射: {len(new_mapping)} 个字段")
            for db_field, display_name in new_mapping.items():
                print(f"  {db_field} → {display_name}")
            
            # 保存新的字段映射
            success = config_sync.save_mapping(table_name, new_mapping)
            
            if success:
                print(f"✓ 字段映射已更新: {table_name}")
            else:
                print(f"✗ 字段映射更新失败: {table_name}")
                
        except Exception as e:
            print(f"✗ 处理表失败 {table_name}: {e}")
    
    print(f"\n修复完成!")
    print("请重新启动系统以查看效果。")

def create_backup_mapping():
    """创建字段映射配置的备份"""
    config_file = "state/data/field_mappings.json"
    backup_file = f"state/data/field_mappings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        if os.path.exists(config_file):
            import shutil
            shutil.copy2(config_file, backup_file)
            print(f"✓ 已创建备份: {backup_file}")
            return True
    except Exception as e:
        print(f"✗ 创建备份失败: {e}")
        return False

if __name__ == "__main__":
    # 先创建备份
    print("创建配置文件备份...")
    create_backup_mapping()
    
    # 执行修复
    fix_field_mapping_display_issue()
