{"timestamp": "2025-06-27T17:58:47.336337", "tests": {"specialized_templates": {"status": "passed", "template_count": 4, "detection_results": {"离休人员工资表": "retired_employees", "退休人员工资表": "pension_employees", "全部在职人员工资表": "active_employees", "A岗职工工资表": "a_grade_employees"}}, "field_mapping_generation": {"status": "passed", "mapping_results": {"离休人员工资表": {"template_key": "retired_employees", "mapping_count": 21, "sample_mappings": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名"}}, "退休人员工资表": {"template_key": "pension_employees", "mapping_count": 32, "sample_mappings": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名"}}, "全部在职人员工资表": {"template_key": "active_employees", "mapping_count": 28, "sample_mappings": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名"}}, "A岗职工工资表": {"template_key": "a_grade_employees", "mapping_count": 26, "sample_mappings": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名"}}}}, "table_structure_comparison": {"status": "passed", "comparison_results": {"salary_data_2027_05_active_employees": {"type": "old_generic", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}, "salary_data_2027_05_retired_employees": {"type": "old_generic", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}, "salary_data_2027_05_pension_employees": {"type": "old_generic", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}, "salary_data_2027_05_a_grade_employees": {"type": "old_generic", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}, "salary_data_2027_06": {"type": "new_specialized", "field_count": 49, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at", "2019待遇调整", "人员代码", "序号", "公积", "增发一次性生活补贴", "2020待遇调整", "2016待遇调整", "人员类别代码", "离退休生活补贴", "离休补贴", "结余津贴", "import_time", "合计", "2018待遇调整", "基本离休费", "生活补贴", "姓名", "增资预付", "住房补贴", "2022待遇调整", "2021待遇调整", "部门名称", "data_source", "津贴", "物业补贴", "基本退休费", "2017待遇调整", "护理费", "应发工资", "补发", "保险扣款", "借支", "2023待遇调整"]}}, "improvement": {"old_field_count": 16, "new_field_count": 49, "improvement_count": 33, "improvement_percent": 206.25}}, "data_integrity": {"status": "passed", "data_integrity_results": {"salary_data_2027_06": {"total_records": 5, "valid_records": 5, "integrity_rate": 100.0}}}, "system_integration": {"status": "passed", "integration_results": {"salary_data_2027_06": {"mapping_save_success": true, "test_mapping_count": 10}}}}, "summary": {"total_tests": 5, "passed_tests": 5, "failed_tests": 0, "warnings": 0, "success_rate": 100.0}}