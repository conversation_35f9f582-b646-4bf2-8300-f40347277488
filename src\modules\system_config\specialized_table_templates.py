#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
4类工资表专用表结构模板

提供离休、退休、在职、A岗4类人员工资表的专用数据库表结构定义，
替代原有的通用模板，实现真正的差异化存储。

创建时间: 2025-06-27
"""

from typing import List, Tuple, Dict, Any
from dataclasses import dataclass

@dataclass
class FieldDefinition:
    """字段定义"""
    name: str
    type: str  # TEXT, INTEGER, REAL, BLOB
    nullable: bool = True
    default: Any = None
    unique: bool = False
    description: str = ""

class SpecializedTableTemplates:
    """4类工资表专用模板管理器"""
    
    def __init__(self):
        self._templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, List[FieldDefinition]]:
        """初始化所有专用模板"""
        return {
            "retired_employees": self._get_retired_employee_template(),
            "pension_employees": self._get_pension_employee_template(),
            "active_employees": self._get_active_employee_template(),
            "a_grade_employees": self._get_a_grade_employee_template()
        }
    
    def _get_retired_employee_template(self) -> List[FieldDefinition]:
        """离休人员工资表模板（21个字段）"""
        return [
            FieldDefinition("id", "INTEGER", False, None, True, "自增主键"),
            FieldDefinition("sequence_number", "INTEGER", True, None, False, "序号"),
            FieldDefinition("employee_id", "TEXT", False, None, False, "人员代码"),
            FieldDefinition("employee_name", "TEXT", False, None, False, "姓名"),
            FieldDefinition("department", "TEXT", True, None, False, "部门名称"),
            FieldDefinition("basic_retirement_salary", "REAL", True, "0", False, "基本离休费"),
            FieldDefinition("balance_allowance", "REAL", True, "0", False, "结余津贴"),
            FieldDefinition("living_allowance", "REAL", True, "0", False, "生活补贴"),
            FieldDefinition("housing_allowance", "REAL", True, "0", False, "住房补贴"),
            FieldDefinition("property_allowance", "REAL", True, "0", False, "物业补贴"),
            FieldDefinition("retirement_allowance", "REAL", True, "0", False, "离休补贴"),
            FieldDefinition("nursing_fee", "REAL", True, "0", False, "护理费"),
            FieldDefinition("one_time_living_allowance", "REAL", True, "0", False, "增发一次性生活补贴"),
            FieldDefinition("supplement", "REAL", True, "0", False, "补发"),
            FieldDefinition("total", "REAL", True, "0", False, "合计"),
            FieldDefinition("advance", "REAL", True, "0", False, "借支"),
            FieldDefinition("remarks", "TEXT", True, None, False, "备注"),
            FieldDefinition("month", "TEXT", False, None, False, "月份"),
            FieldDefinition("year", "INTEGER", False, None, False, "年份"),
            FieldDefinition("created_at", "TEXT", False, None, False, "创建时间"),
            FieldDefinition("updated_at", "TEXT", False, None, False, "更新时间")
        ]
    
    def _get_pension_employee_template(self) -> List[FieldDefinition]:
        """退休人员工资表模板（32个字段）"""
        return [
            FieldDefinition("id", "INTEGER", False, None, True, "自增主键"),
            FieldDefinition("sequence_number", "INTEGER", True, None, False, "序号"),
            FieldDefinition("employee_id", "TEXT", False, None, False, "人员代码"),
            FieldDefinition("employee_name", "TEXT", False, None, False, "姓名"),
            FieldDefinition("department", "TEXT", True, None, False, "部门名称"),
            FieldDefinition("employee_type_code", "TEXT", True, None, False, "人员类别代码"),
            FieldDefinition("basic_retirement_salary", "REAL", True, "0", False, "基本退休费"),
            FieldDefinition("allowance", "REAL", True, "0", False, "津贴"),
            FieldDefinition("balance_allowance", "REAL", True, "0", False, "结余津贴"),
            FieldDefinition("retirement_living_allowance", "REAL", True, "0", False, "离退休生活补贴"),
            FieldDefinition("nursing_fee", "REAL", True, "0", False, "护理费"),
            FieldDefinition("property_allowance", "REAL", True, "0", False, "物业补贴"),
            FieldDefinition("housing_allowance", "REAL", True, "0", False, "住房补贴"),
            FieldDefinition("salary_advance", "REAL", True, "0", False, "增资预付"),
            FieldDefinition("adjustment_2016", "REAL", True, "0", False, "2016待遇调整"),
            FieldDefinition("adjustment_2017", "REAL", True, "0", False, "2017待遇调整"),
            FieldDefinition("adjustment_2018", "REAL", True, "0", False, "2018待遇调整"),
            FieldDefinition("adjustment_2019", "REAL", True, "0", False, "2019待遇调整"),
            FieldDefinition("adjustment_2020", "REAL", True, "0", False, "2020待遇调整"),
            FieldDefinition("adjustment_2021", "REAL", True, "0", False, "2021待遇调整"),
            FieldDefinition("adjustment_2022", "REAL", True, "0", False, "2022待遇调整"),
            FieldDefinition("adjustment_2023", "REAL", True, "0", False, "2023待遇调整"),
            FieldDefinition("supplement", "REAL", True, "0", False, "补发"),
            FieldDefinition("advance", "REAL", True, "0", False, "借支"),
            FieldDefinition("total_salary", "REAL", True, "0", False, "应发工资"),
            FieldDefinition("provident_fund", "REAL", True, "0", False, "公积"),
            FieldDefinition("insurance_deduction", "REAL", True, "0", False, "保险扣款"),
            FieldDefinition("remarks", "TEXT", True, None, False, "备注"),
            FieldDefinition("month", "TEXT", False, None, False, "月份"),
            FieldDefinition("year", "INTEGER", False, None, False, "年份"),
            FieldDefinition("created_at", "TEXT", False, None, False, "创建时间"),
            FieldDefinition("updated_at", "TEXT", False, None, False, "更新时间")
        ]
    
    def _get_active_employee_template(self) -> List[FieldDefinition]:
        """全部在职人员工资表模板（28个字段）"""
        return [
            FieldDefinition("id", "INTEGER", False, None, True, "自增主键"),
            FieldDefinition("sequence_number", "INTEGER", True, None, False, "序号"),
            FieldDefinition("employee_id", "TEXT", False, None, False, "工号"),
            FieldDefinition("employee_name", "TEXT", False, None, False, "姓名"),
            FieldDefinition("department", "TEXT", True, None, False, "部门名称"),
            FieldDefinition("employee_type_code", "TEXT", True, None, False, "人员类别代码"),
            FieldDefinition("employee_type", "TEXT", True, None, False, "人员类别"),
            FieldDefinition("position_salary_2025", "REAL", True, "0", False, "2025年岗位工资"),
            FieldDefinition("grade_salary_2025", "REAL", True, "0", False, "2025年薪级工资"),
            FieldDefinition("allowance", "REAL", True, "0", False, "津贴"),
            FieldDefinition("balance_allowance", "REAL", True, "0", False, "结余津贴"),
            FieldDefinition("basic_performance_2025", "REAL", True, "0", False, "2025年基础性绩效"),
            FieldDefinition("health_fee", "REAL", True, "0", False, "卫生费"),
            FieldDefinition("transport_allowance", "REAL", True, "0", False, "交通补贴"),
            FieldDefinition("property_allowance", "REAL", True, "0", False, "物业补贴"),
            FieldDefinition("housing_allowance", "REAL", True, "0", False, "住房补贴"),
            FieldDefinition("car_allowance", "REAL", True, "0", False, "车补"),
            FieldDefinition("communication_allowance", "REAL", True, "0", False, "通讯补贴"),
            FieldDefinition("performance_bonus_2025", "REAL", True, "0", False, "2025年奖励性绩效预发"),
            FieldDefinition("supplement", "REAL", True, "0", False, "补发"),
            FieldDefinition("advance", "REAL", True, "0", False, "借支"),
            FieldDefinition("total_salary", "REAL", True, "0", False, "应发工资"),
            FieldDefinition("provident_fund_2025", "REAL", True, "0", False, "2025公积金"),
            FieldDefinition("pension_insurance", "REAL", True, "0", False, "代扣代存养老保险"),
            FieldDefinition("month", "TEXT", False, None, False, "月份"),
            FieldDefinition("year", "INTEGER", False, None, False, "年份"),
            FieldDefinition("created_at", "TEXT", False, None, False, "创建时间"),
            FieldDefinition("updated_at", "TEXT", False, None, False, "更新时间")
        ]
    
    def _get_a_grade_employee_template(self) -> List[FieldDefinition]:
        """A岗职工工资表模板（26个字段）"""
        return [
            FieldDefinition("id", "INTEGER", False, None, True, "自增主键"),
            FieldDefinition("sequence_number", "INTEGER", True, None, False, "序号"),
            FieldDefinition("employee_id", "TEXT", False, None, False, "工号"),
            FieldDefinition("employee_name", "TEXT", False, None, False, "姓名"),
            FieldDefinition("department", "TEXT", True, None, False, "部门名称"),
            FieldDefinition("employee_type", "TEXT", True, None, False, "人员类别"),
            FieldDefinition("employee_type_code", "TEXT", True, None, False, "人员类别代码"),
            FieldDefinition("position_salary_2025", "REAL", True, "0", False, "2025年岗位工资"),
            FieldDefinition("seniority_salary_2025", "REAL", True, "0", False, "2025年校龄工资"),
            FieldDefinition("allowance", "REAL", True, "0", False, "津贴"),
            FieldDefinition("balance_allowance", "REAL", True, "0", False, "结余津贴"),
            FieldDefinition("basic_performance_2025", "REAL", True, "0", False, "2025年基础性绩效"),
            FieldDefinition("health_fee", "REAL", True, "0", False, "卫生费"),
            FieldDefinition("living_allowance_2025", "REAL", True, "0", False, "2025年生活补贴"),
            FieldDefinition("car_allowance", "REAL", True, "0", False, "车补"),
            FieldDefinition("performance_bonus_2025", "REAL", True, "0", False, "2025年奖励性绩效预发"),
            FieldDefinition("supplement", "REAL", True, "0", False, "补发"),
            FieldDefinition("advance", "REAL", True, "0", False, "借支"),
            FieldDefinition("total_salary", "REAL", True, "0", False, "应发工资"),
            FieldDefinition("provident_fund_2025", "REAL", True, "0", False, "2025公积金"),
            FieldDefinition("insurance_deduction", "REAL", True, "0", False, "保险扣款"),
            FieldDefinition("pension_insurance", "REAL", True, "0", False, "代扣代存养老保险"),
            FieldDefinition("month", "TEXT", False, None, False, "月份"),
            FieldDefinition("year", "INTEGER", False, None, False, "年份"),
            FieldDefinition("created_at", "TEXT", False, None, False, "创建时间"),
            FieldDefinition("updated_at", "TEXT", False, None, False, "更新时间")
        ]
    
    def get_template(self, template_key: str) -> List[FieldDefinition]:
        """获取指定模板"""
        return self._templates.get(template_key, [])
    
    def get_all_templates(self) -> Dict[str, List[FieldDefinition]]:
        """获取所有模板"""
        return self._templates.copy()
    
    def get_template_info(self) -> Dict[str, Dict[str, Any]]:
        """获取模板信息摘要"""
        info = {}
        for template_key, fields in self._templates.items():
            info[template_key] = {
                "field_count": len(fields),
                "description": self._get_template_description(template_key),
                "key_fields": [f.name for f in fields if f.name in ["employee_id", "employee_name", "total_salary"]]
            }
        return info
    
    def _get_template_description(self, template_key: str) -> str:
        """获取模板描述"""
        descriptions = {
            "retired_employees": "离休人员工资表 - 包含基本离休费、护理费等专用字段",
            "pension_employees": "退休人员工资表 - 包含基本退休费、历年调整等专用字段",
            "active_employees": "全部在职人员工资表 - 包含岗位工资、薪级工资等专用字段",
            "a_grade_employees": "A岗职工工资表 - 包含校龄工资、生活补贴等专用字段"
        }
        return descriptions.get(template_key, "未知模板")
    
    def detect_template_by_headers(self, headers: List[str]) -> str:
        """根据Excel表头检测应使用的模板"""
        header_set = set(headers)
        
        # 离休人员特征字段
        retired_features = {"基本离休费", "离休补贴", "护理费", "增发一次性生活补贴"}
        if len(retired_features.intersection(header_set)) >= 2:
            return "retired_employees"
        
        # 退休人员特征字段  
        pension_features = {"基本退休费", "2016待遇调整", "2017待遇调整", "增资预付"}
        if len(pension_features.intersection(header_set)) >= 2:
            return "pension_employees"
        
        # A岗职工特征字段
        a_grade_features = {"校龄工资", "2025年校龄工资", "2025年生活补贴"}
        if len(a_grade_features.intersection(header_set)) >= 1:
            return "a_grade_employees"
        
        # 在职人员特征字段
        active_features = {"岗位工资", "薪级工资", "基础性绩效", "2025年岗位工资", "2025年薪级工资"}
        if len(active_features.intersection(header_set)) >= 2:
            return "active_employees"
        
        return "salary_data"  # 默认通用模板
