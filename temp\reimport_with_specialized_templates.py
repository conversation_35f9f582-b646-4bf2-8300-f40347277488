#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段：使用专用模板重新导入Excel文件
使用新的专用导入逻辑重新导入工资表Excel文件，验证专用表结构创建
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from pathlib import Path
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def reimport_with_specialized_templates():
    """使用专用模板重新导入Excel文件"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("第三阶段：使用专用模板重新导入Excel文件")
    print("=" * 80)
    
    # 1. 初始化组件
    print("\n1. 初始化导入组件")
    print("-" * 50)
    
    try:
        table_manager = DynamicTableManager()
        importer = MultiSheetImporter(table_manager)
        print("✓ 导入组件初始化成功")
    except Exception as e:
        print(f"✗ 导入组件初始化失败: {e}")
        return False
    
    # 2. 查找Excel文件
    print("\n2. 查找Excel文件")
    print("-" * 50)
    
    excel_files = []
    data_dir = Path("data")
    
    if data_dir.exists():
        excel_files = list(data_dir.glob("*.xlsx")) + list(data_dir.glob("*.xls"))
        print(f"✓ 找到 {len(excel_files)} 个Excel文件")
        for file in excel_files:
            print(f"  - {file}")
    else:
        print("✗ 数据目录不存在")
        return False
    
    if not excel_files:
        print("⚠ 未找到Excel文件，创建测试数据")
        return create_test_import_data()
    
    # 3. 选择要重新导入的文件
    print("\n3. 重新导入Excel文件")
    print("-" * 50)
    
    import_results = []
    
    for excel_file in excel_files[:1]:  # 只处理第一个文件作为测试
        print(f"\n处理文件: {excel_file}")
        
        try:
            # 读取Excel文件信息
            excel_data = pd.ExcelFile(excel_file)
            sheet_names = excel_data.sheet_names
            print(f"  Sheet数量: {len(sheet_names)}")
            
            # 为每个Sheet检测模板类型
            sheet_info = {}
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=1)
                    headers = list(df.columns)
                    template_key = importer.specialized_templates.detect_template_by_headers(headers)
                    sheet_info[sheet_name] = {
                        "headers": headers,
                        "template_key": template_key,
                        "header_count": len(headers)
                    }
                    print(f"    {sheet_name}: {template_key} ({len(headers)} 个字段)")
                except Exception as e:
                    print(f"    {sheet_name}: 读取失败 - {e}")
            
            # 使用专用导入逻辑导入
            year, month = 2027, 6  # 使用新的年月避免与现有数据冲突
            
            import_config = {
                "file_path": str(excel_file),
                "year": year,
                "month": month,
                "starting_row": 1,
                "import_mode": "multi_sheet",
                "import_strategy": "separate_tables",
                "table_template": "specialized_auto_detect"  # 新的专用模板标识
            }
            
            print(f"  开始导入 (年月: {year}-{month:02d})")
            
            # 执行导入
            result = importer.import_excel_file(
                file_path=import_config["file_path"],
                year=import_config["year"],
                month=import_config["month"]
            )
            
            import_results.append({
                "file": str(excel_file),
                "result": result,
                "sheet_info": sheet_info
            })
            
            if result.get("success", False):
                print(f"  ✓ 导入成功")
                print(f"    成功Sheet: {result.get('successful_sheets', 0)}")
                print(f"    失败Sheet: {result.get('failed_sheets', 0)}")
            else:
                print(f"  ✗ 导入失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"  ✗ 处理文件失败: {e}")
            import_results.append({
                "file": str(excel_file),
                "result": {"success": False, "error": str(e)},
                "sheet_info": {}
            })
    
    # 4. 验证导入结果
    print("\n4. 验证导入结果")
    print("-" * 50)
    
    try:
        # 检查新创建的表
        new_tables = []
        import sqlite3
        
        db_path = Path("data/db/salary_system.db")
        if db_path.exists():
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data_2027_06_%'")
            new_tables = [row[0] for row in cursor.fetchall()]
            conn.close()
        
        print(f"✓ 新创建的表数量: {len(new_tables)}")
        
        for table_name in new_tables:
            try:
                columns = table_manager.get_table_columns(table_name)
                field_count = len(columns)
                
                # 检查是否为专用表结构
                if field_count > 16:
                    print(f"  ✓ {table_name}: {field_count} 个字段 (专用结构)")
                else:
                    print(f"  ⚠ {table_name}: {field_count} 个字段 (通用结构)")
                    
            except Exception as e:
                print(f"  ✗ {table_name}: 检查失败 - {e}")
    
    except Exception as e:
        print(f"✗ 验证导入结果失败: {e}")
    
    # 5. 生成导入报告
    print("\n5. 生成导入报告")
    print("-" * 50)
    
    try:
        import json
        from datetime import datetime
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "import_results": import_results,
            "new_tables": new_tables if 'new_tables' in locals() else [],
            "summary": {
                "total_files": len(excel_files),
                "processed_files": len(import_results),
                "successful_imports": sum(1 for r in import_results if r["result"].get("success", False)),
                "new_tables_created": len(new_tables) if 'new_tables' in locals() else 0
            }
        }
        
        report_file = Path("temp/reimport_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 导入报告保存成功: {report_file}")
        print(f"  处理文件: {report['summary']['processed_files']}")
        print(f"  成功导入: {report['summary']['successful_imports']}")
        print(f"  新建表数: {report['summary']['new_tables_created']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 生成导入报告失败: {e}")
        return False

def create_test_import_data():
    """创建测试导入数据"""
    print("\n创建测试导入数据")
    print("-" * 50)
    
    try:
        # 创建4类工资表的测试数据
        test_data = {
            "离休人员工资表": {
                "序号": [1, 2],
                "人员代码": ["LX001", "LX002"],
                "姓名": ["张离休", "李离休"],
                "部门名称": ["离休办", "离休办"],
                "基本离休费": [8000.0, 8500.0],
                "结余津贴": [500.0, 600.0],
                "生活补贴": [300.0, 300.0],
                "住房补贴": [1000.0, 1000.0],
                "物业补贴": [200.0, 200.0],
                "离休补贴": [400.0, 400.0],
                "护理费": [800.0, 900.0],
                "增发一次性生活补贴": [0.0, 0.0],
                "补发": [0.0, 100.0],
                "合计": [11200.0, 11900.0],
                "借支": [0.0, 0.0],
                "备注": ["", ""]
            },
            "退休人员工资表": {
                "序号": [1, 2, 3],
                "人员代码": ["TX001", "TX002", "TX003"],
                "姓名": ["王退休", "赵退休", "钱退休"],
                "部门名称": ["退休办", "退休办", "退休办"],
                "人员类别代码": ["T01", "T02", "T01"],
                "基本退休费": [6000.0, 6200.0, 5800.0],
                "津贴": [400.0, 450.0, 380.0],
                "结余津贴": [300.0, 320.0, 280.0],
                "离退休生活补贴": [500.0, 500.0, 500.0],
                "护理费": [600.0, 700.0, 550.0],
                "物业补贴": [200.0, 200.0, 200.0],
                "住房补贴": [800.0, 800.0, 800.0],
                "增资预付": [100.0, 120.0, 90.0],
                "2016待遇调整": [50.0, 60.0, 45.0],
                "2017待遇调整": [80.0, 90.0, 70.0],
                "2018待遇调整": [100.0, 110.0, 95.0],
                "2019待遇调整": [120.0, 130.0, 110.0],
                "2020待遇调整": [150.0, 160.0, 140.0],
                "2021待遇调整": [180.0, 190.0, 170.0],
                "2022待遇调整": [200.0, 210.0, 190.0],
                "2023待遇调整": [220.0, 230.0, 210.0],
                "补发": [0.0, 50.0, 0.0],
                "借支": [0.0, 0.0, 100.0],
                "应发工资": [9900.0, 10420.0, 9340.0],
                "公积": [500.0, 520.0, 480.0],
                "保险扣款": [200.0, 210.0, 190.0],
                "备注": ["", "", ""]
            }
        }
        
        # 保存为Excel文件
        test_file = Path("data/test_specialized_import.xlsx")
        test_file.parent.mkdir(parents=True, exist_ok=True)
        
        with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
            for sheet_name, data in test_data.items():
                df = pd.DataFrame(data)
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✓ 测试数据创建成功: {test_file}")
        print(f"  包含Sheet: {list(test_data.keys())}")
        
        # 使用测试数据进行导入
        table_manager = DynamicTableManager()
        importer = MultiSheetImporter(table_manager)
        
        import_config = {
            "file_path": str(test_file),
            "year": 2027,
            "month": 6,
            "starting_row": 1,
            "import_mode": "multi_sheet",
            "import_strategy": "separate_tables",
            "table_template": "specialized_auto_detect"
        }
        
        print(f"\n开始导入测试数据...")
        result = importer.import_excel_file(
            file_path=import_config["file_path"],
            year=import_config["year"],
            month=import_config["month"]
        )
        
        if result.get("success", False):
            print(f"✓ 测试数据导入成功")
            return True
        else:
            print(f"✗ 测试数据导入失败: {result.get('message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"✗ 创建测试数据失败: {e}")
        return False

if __name__ == "__main__":
    success = reimport_with_specialized_templates()
    if success:
        print(f"\n✅ 重新导入完成!")
    else:
        print(f"\n❌ 重新导入失败!")
