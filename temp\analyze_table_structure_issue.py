#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析数据库表结构问题
验证4类工资表是否使用了相同的通用模板而不是专用结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def analyze_table_structure_issue():
    """分析表结构问题"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("数据库表结构问题分析")
    print("=" * 80)
    
    table_manager = DynamicTableManager()
    
    # 4类工资表
    tables_to_check = [
        "salary_data_2027_05_active_employees",      # 全部在职人员
        "salary_data_2027_05_a_grade_employees",     # A岗职工
        "salary_data_2027_05_pension_employees",     # 退休人员
        "salary_data_2027_05_retired_employees"      # 离休人员
    ]
    
    print("\n1. 检查4类工资表的数据库结构")
    print("-" * 50)
    
    all_table_structures = {}
    
    for table_name in tables_to_check:
        try:
            if table_manager.table_exists(table_name):
                columns = table_manager.get_table_columns(table_name)
                field_names = [col.get('name', '') for col in columns]
                all_table_structures[table_name] = field_names
                
                print(f"\n表: {table_name}")
                print(f"字段数量: {len(field_names)}")
                print(f"字段列表: {field_names}")
            else:
                print(f"\n表不存在: {table_name}")
                
        except Exception as e:
            print(f"\n检查表失败 {table_name}: {e}")
    
    print("\n2. 对比表结构是否相同")
    print("-" * 50)
    
    if len(all_table_structures) > 1:
        # 获取第一个表的结构作为基准
        base_table = list(all_table_structures.keys())[0]
        base_structure = all_table_structures[base_table]
        
        all_same = True
        for table_name, structure in all_table_structures.items():
            if structure != base_structure:
                all_same = False
                print(f"✗ {table_name} 结构与 {base_table} 不同")
                print(f"  差异字段: {set(structure) - set(base_structure)}")
            else:
                print(f"✓ {table_name} 结构与 {base_table} 相同")
        
        if all_same:
            print(f"\n🚨 问题确认：所有4类工资表使用了完全相同的数据库结构！")
            print(f"   共同结构: {base_structure}")
        else:
            print(f"\n✓ 各表有不同的结构")
    
    print("\n3. 分析应该的表结构")
    print("-" * 50)
    
    # 根据Excel表头分析应该的字段结构
    expected_structures = {
        "离休人员工资表": [
            "序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", 
            "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", 
            "增发一次性生活补贴", "补发", "合计", "借支", "备注"
        ],
        "退休人员工资表": [
            "序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", 
            "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", 
            "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", 
            "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", 
            "补发", "借支", "应发工资", "公积", "保险扣款", "备注"
        ],
        "全部在职人员工资表": [
            "序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", 
            "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", 
            "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", 
            "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"
        ],
        "A岗职工工资表": [
            "序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", 
            "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", 
            "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", 
            "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"
        ]
    }
    
    for table_type, expected_fields in expected_structures.items():
        print(f"\n{table_type}:")
        print(f"  应有字段数: {len(expected_fields)}")
        print(f"  特色字段: {expected_fields[:5]}...")
    
    print("\n4. 问题根源分析")
    print("-" * 50)
    
    print("问题根源：")
    print("1. 数据导入时使用了统一的 'salary_data' 模板")
    print("2. 所有工资表都创建为相同的16个通用字段")
    print("3. 没有根据Excel表头的实际字段创建专用表结构")
    print("4. 导致丰富的Excel字段信息丢失，只能映射到有限的通用字段")
    
    print("\n5. 解决方案建议")
    print("-" * 50)
    
    print("短期解决方案：")
    print("1. 保持现有通用结构，优化字段映射配置")
    print("2. 将Excel的专用字段映射到通用字段（如将'基本离休费'映射到'basic_salary'）")
    print("3. 在显示层通过字段映射显示正确的中文名称")
    
    print("\n长期解决方案：")
    print("1. 为4类工资表创建专用的表结构模板")
    print("2. 修改数据导入逻辑，根据表类型选择对应模板")
    print("3. 重新导入数据，使用专用表结构")
    print("4. 建立完整的字段映射体系")

if __name__ == "__main__":
    analyze_table_structure_issue()
