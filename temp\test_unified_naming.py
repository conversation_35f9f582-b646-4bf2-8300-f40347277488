#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试统一命名格式后的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager

def test_unified_table_naming():
    """测试统一表命名格式"""
    print("=== 测试统一表命名格式 ===")
    
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    # 获取2027年9月的所有表
    tables = table_manager.get_table_list(table_type='salary_data')
    sept_2027_tables = [t for t in tables if '2027_09' in t.get('table_name', '')]
    
    print(f"📋 2027年9月相关表: {len(sept_2027_tables)} 个")
    
    all_have_suffix = True
    for table in sept_2027_tables:
        table_name = table.get('table_name', 'Unknown')
        display_name = table.get('display_name', 'Unknown')
        
        # 检查是否符合统一格式
        parts = table_name.split('_')
        if len(parts) >= 5:
            print(f"✅ {table_name}: {display_name} (格式正确)")
        else:
            print(f"❌ {table_name}: {display_name} (格式不正确)")
            all_have_suffix = False
    
    if all_have_suffix:
        print("\n🎉 所有表都使用统一的命名格式!")
    else:
        print("\n⚠️  仍有表使用不统一的命名格式")
    
    return all_have_suffix

def test_navigation_tree_consistency():
    """测试导航树一致性"""
    print("\n=== 测试导航树一致性 ===")
    
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    # 获取导航树数据
    navigation_data = table_manager.get_navigation_tree_data()
    
    if 2027 in navigation_data and 9 in navigation_data[2027]:
        items = navigation_data[2027][9]
        print(f"🎯 2027年9月包含 {len(items)} 个导航项:")
        
        expected_types = {'retired_employees', 'pension_employees', 'active_employees', 'a_grade_employees'}
        actual_types = set()
        
        for item in items:
            table_name = item['table_name']
            display_name = item['display_name']
            
            # 解析员工类型
            parts = table_name.split('_')
            if len(parts) >= 5:
                employee_type = '_'.join(parts[4:])
                actual_types.add(employee_type)
                print(f"  - {display_name} ({table_name}) -> {employee_type}")
            else:
                print(f"  ❌ {display_name} ({table_name}) -> 格式错误")
        
        if actual_types == expected_types:
            print("\n✅ 所有预期的员工类型都存在")
            return True
        else:
            missing = expected_types - actual_types
            extra = actual_types - expected_types
            if missing:
                print(f"\n❌ 缺少员工类型: {missing}")
            if extra:
                print(f"\n❌ 多余员工类型: {extra}")
            return False
    else:
        print("❌ 2027年9月数据未找到")
        return False

def test_field_mappings_consistency():
    """测试字段映射一致性"""
    print("\n=== 测试字段映射一致性 ===")
    
    config_sync = ConfigSyncManager()
    
    # 测试所有2027年9月的表
    expected_tables = [
        "salary_data_2027_09_retired_employees",
        "salary_data_2027_09_pension_employees", 
        "salary_data_2027_09_active_employees",
        "salary_data_2027_09_a_grade_employees"
    ]
    
    all_have_mappings = True
    for table_name in expected_tables:
        mapping = config_sync.load_mapping(table_name)
        if mapping:
            print(f"✅ {table_name}: 有字段映射 ({len(mapping)} 个字段)")
        else:
            print(f"❌ {table_name}: 无字段映射")
            all_have_mappings = False
    
    return all_have_mappings

def main():
    """主测试函数"""
    print("🔧 测试统一命名格式后的效果...\n")
    
    try:
        # 测试表命名一致性
        naming_ok = test_unified_table_naming()
        
        # 测试导航树一致性
        navigation_ok = test_navigation_tree_consistency()
        
        # 测试字段映射一致性
        mapping_ok = test_field_mappings_consistency()
        
        print(f"\n📊 测试结果总结:")
        print(f"   表命名格式: {'✅ 统一' if naming_ok else '❌ 不统一'}")
        print(f"   导航树显示: {'✅ 正常' if navigation_ok else '❌ 异常'}")
        print(f"   字段映射: {'✅ 完整' if mapping_ok else '❌ 缺失'}")
        
        if naming_ok and navigation_ok and mapping_ok:
            print(f"\n🎉 所有测试通过！表命名一致性问题已完全解决。")
        else:
            print(f"\n⚠️  仍有问题需要解决。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()
