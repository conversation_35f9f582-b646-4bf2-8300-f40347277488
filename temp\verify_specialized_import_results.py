#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证专用模板导入结果
检查新创建的表结构和字段映射配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
import json
from pathlib import Path
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.utils.log_config import setup_logger

def verify_specialized_import_results():
    """验证专用模板导入结果"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("验证专用模板导入结果")
    print("=" * 80)
    
    # 1. 检查数据库中的新表
    print("\n1. 检查数据库中的新表")
    print("-" * 50)
    
    try:
        db_path = Path("data/db/salary_system.db")
        if db_path.exists():
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查找2027年6月的表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data_2027_06%'")
            new_tables = [row[0] for row in cursor.fetchall()]
            
            print(f"✓ 找到 {len(new_tables)} 个新表:")
            for table_name in new_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  - {table_name}: {count} 条记录")
            
            conn.close()
        else:
            print("✗ 数据库文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 检查数据库失败: {e}")
        return False
    
    # 2. 检查表结构
    print("\n2. 检查表结构")
    print("-" * 50)
    
    try:
        table_manager = DynamicTableManager()
        
        for table_name in new_tables:
            print(f"\n表: {table_name}")
            columns = table_manager.get_table_columns(table_name)
            print(f"  字段数量: {len(columns)}")
            
            # 显示前10个字段
            print("  字段列表:")
            for i, col in enumerate(columns[:10]):
                field_name = col.get('name', 'unknown')
                field_type = col.get('type', 'unknown')
                print(f"    {i+1:2d}. {field_name} ({field_type})")
            
            if len(columns) > 10:
                print(f"    ... 还有 {len(columns) - 10} 个字段")
            
            # 判断是否为专用结构
            if len(columns) > 16:
                print(f"  ✓ 专用表结构 ({len(columns)} 字段)")
            else:
                print(f"  ⚠ 通用表结构 ({len(columns)} 字段)")
                
    except Exception as e:
        print(f"✗ 检查表结构失败: {e}")
    
    # 3. 检查字段映射配置
    print("\n3. 检查字段映射配置")
    print("-" * 50)
    
    try:
        config_sync = ConfigSyncManager()
        
        for table_name in new_tables:
            print(f"\n表: {table_name}")
            mapping = config_sync.load_mapping(table_name)
            
            if mapping:
                print(f"  ✓ 字段映射配置存在: {len(mapping)} 个字段")
                
                # 显示前5个映射
                print("  字段映射:")
                for i, (db_field, display_name) in enumerate(mapping.items()):
                    if i < 5:
                        print(f"    {db_field} -> {display_name}")
                if len(mapping) > 5:
                    print(f"    ... 还有 {len(mapping) - 5} 个映射")
            else:
                print("  ✗ 字段映射配置不存在")
                
    except Exception as e:
        print(f"✗ 检查字段映射配置失败: {e}")
    
    # 4. 检查数据内容
    print("\n4. 检查数据内容")
    print("-" * 50)
    
    try:
        for table_name in new_tables:
            print(f"\n表: {table_name}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]
            
            # 获取前3条数据
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
            rows = cursor.fetchall()
            
            print(f"  数据样本 (前3条):")
            for i, row in enumerate(rows):
                print(f"    记录 {i+1}:")
                for j, (col_name, value) in enumerate(zip(column_names, row)):
                    if j < 5:  # 只显示前5个字段
                        print(f"      {col_name}: {value}")
                if len(column_names) > 5:
                    print(f"      ... 还有 {len(column_names) - 5} 个字段")
            
            conn.close()
            
    except Exception as e:
        print(f"✗ 检查数据内容失败: {e}")
    
    # 5. 对比分析
    print("\n5. 对比分析")
    print("-" * 50)
    
    try:
        # 对比新表和旧表的结构差异
        old_table_example = "salary_data_2027_05_active_employees"
        
        if table_manager.table_exists(old_table_example):
            old_columns = table_manager.get_table_columns(old_table_example)
            print(f"旧表 {old_table_example}: {len(old_columns)} 个字段")
            
            for new_table in new_tables:
                new_columns = table_manager.get_table_columns(new_table)
                print(f"新表 {new_table}: {len(new_columns)} 个字段")
                
                if len(new_columns) > len(old_columns):
                    improvement = len(new_columns) - len(old_columns)
                    print(f"  ✓ 字段数量提升: +{improvement} 个字段 (+{improvement/len(old_columns)*100:.1f}%)")
                else:
                    print(f"  ⚠ 字段数量未提升")
        else:
            print("⚠ 未找到对比的旧表")
            
    except Exception as e:
        print(f"✗ 对比分析失败: {e}")
    
    # 6. 生成验证报告
    print("\n6. 生成验证报告")
    print("-" * 50)
    
    try:
        from datetime import datetime
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "verification_results": {
                "new_tables": new_tables if 'new_tables' in locals() else [],
                "table_structures": {},
                "field_mappings": {},
                "data_samples": {}
            },
            "summary": {
                "total_new_tables": len(new_tables) if 'new_tables' in locals() else 0,
                "specialized_tables": 0,
                "generic_tables": 0
            }
        }
        
        # 收集详细信息
        for table_name in new_tables:
            try:
                columns = table_manager.get_table_columns(table_name)
                mapping = config_sync.load_mapping(table_name)
                
                report["verification_results"]["table_structures"][table_name] = {
                    "field_count": len(columns),
                    "is_specialized": len(columns) > 16,
                    "fields": [col.get('name', '') for col in columns]
                }
                
                report["verification_results"]["field_mappings"][table_name] = {
                    "mapping_exists": mapping is not None,
                    "mapping_count": len(mapping) if mapping else 0
                }
                
                if len(columns) > 16:
                    report["summary"]["specialized_tables"] += 1
                else:
                    report["summary"]["generic_tables"] += 1
                    
            except Exception as e:
                print(f"收集 {table_name} 信息失败: {e}")
        
        # 保存报告
        report_file = Path("temp/verification_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 验证报告保存成功: {report_file}")
        print(f"  新建表数: {report['summary']['total_new_tables']}")
        print(f"  专用表数: {report['summary']['specialized_tables']}")
        print(f"  通用表数: {report['summary']['generic_tables']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 生成验证报告失败: {e}")
        return False

if __name__ == "__main__":
    success = verify_specialized_import_results()
    if success:
        print(f"\n✅ 验证完成!")
    else:
        print(f"\n❌ 验证失败!")
