2025-06-27 19:46:55.197 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 19:46:55.197 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 19:46:55.197 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 19:46:55.197 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 19:46:55.212 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 19:46:55.212 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 19:46:57.753 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 19:46:57.753 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 19:46:57.769 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 19:46:57.769 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 19:46:57.769 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 19:46:57.769 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 19:46:57.769 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 19:46:57.769 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 19:46:57.769 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 19:46:57.785 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 19:46:57.788 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-06-27 19:46:57.788 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 19:46:57.790 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 19:46:57.812 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 19:46:57.819 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 19:46:58.549 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1518 | 菜单栏创建完成
2025-06-27 19:46:58.551 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 19:46:58.551 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 19:46:58.552 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 19:46:58.554 | INFO     | src.gui.prototype.prototype_main_window:__init__:1494 | 菜单栏管理器初始化完成
2025-06-27 19:46:58.555 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 19:46:58.556 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2195 | 管理器设置完成，包含增强版表头管理器
2025-06-27 19:46:58.558 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 19:46:58.562 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 19:46:58.568 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 19:46:58.569 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 19:46:58.571 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 19:46:58.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-06-27 19:46:58.572 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-06-27 19:46:58.573 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 19:46:58.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 19:46:58.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 19:46:58.658 | INFO     | src.modules.data_import.config_sync_manager:_initialize_config:94 | 默认配置文件创建成功，包含4类工资表模板
2025-06-27 19:46:58.659 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:46:58.660 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 19:46:58.733 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 19:46:58.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 19:46:58.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 19:46:58.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 18.60ms
2025-06-27 19:46:58.755 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 19:46:58.763 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 19:46:58.934 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 19:46:58.947 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 19:46:58.948 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 19:46:58.949 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2168 | 快捷键设置完成
2025-06-27 19:46:58.950 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2157 | 主窗口UI设置完成。
2025-06-27 19:46:58.950 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2223 | 信号连接设置完成
2025-06-27 19:46:58.952 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:46:58.952 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2652 | 已加载字段映射信息，共0个表的映射
2025-06-27 19:46:58.953 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 19:46:58.961 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 19:46:58.963 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 19:46:58.964 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.63ms
2025-06-27 19:46:58.965 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 19:46:58.965 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:46:58.965 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 19:46:58.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 19:46:58.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 19:46:58.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.01ms
2025-06-27 19:46:58.968 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 19:46:58.969 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:46:58.970 | INFO     | src.gui.prototype.prototype_main_window:__init__:2109 | 原型主窗口初始化完成
2025-06-27 19:46:59.417 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 19:46:59.421 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 19:46:59.423 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 19:46:59.424 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 19:46:59.424 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1249 | MainWorkspaceArea 响应式适配: sm
2025-06-27 19:46:59.429 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 19:46:59.430 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 19:46:59.430 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 19:46:59.497 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:775 | 找到 123 个匹配类型 'salary_data' 的表
2025-06-27 19:46:59.522 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1301 | 元数据项 salary_data_2027_06 缺少年份或月份信息，已跳过
2025-06-27 19:46:59.534 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1301 | 元数据项 salary_data_2027_08 缺少年份或月份信息，已跳过
2025-06-27 19:46:59.535 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 6 个月份
2025-06-27 19:46:59.537 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 19:46:59.538 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 19:46:59.555 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 19:46:59.588 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 19:46:59.590 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 19:46:59.596 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 19:46:59.598 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 19:46:59.602 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 19:46:59.607 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 19:46:59.607 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 19:46:59.609 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 19:46:59.610 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 30 个月份
2025-06-27 19:46:59.611 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 19:46:59.612 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 19:46:59.812 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 19:46:59.817 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1350 | 开始获取最新工资数据路径...
2025-06-27 19:46:59.876 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:775 | 找到 123 个匹配类型 'salary_data' 的表
2025-06-27 19:46:59.926 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1398 | 找到最新工资数据路径: 工资表 > 2027年 > 08月 > 全部在职人员
2025-06-27 19:46:59.926 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 08月 > 全部在职人员
2025-06-27 19:46:59.928 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 08月 > 全部在职人员
2025-06-27 19:46:59.928 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 08月 > 全部在职人员
2025-06-27 19:46:59.928 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 19:46:59.928 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 19:46:59.928 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 19:46:59.928 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 19:46:59.938 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2743 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 19:46:59.941 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:46:59.942 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 19:46:59.942 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_08_active_employees，第1页，每页50条
2025-06-27 19:46:59.943 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_08_active_employees 第1页
2025-06-27 19:46:59.943 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_08_active_employees 第1页数据，每页50条
2025-06-27 19:46:59.944 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_08_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 19:46:59.953 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_08_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 19:46:59.954 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 19:46:59.955 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_08_active_employees 没有字段映射配置
2025-06-27 19:46:59.956 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，28 个字段，总记录数: 1396
2025-06-27 19:46:59.963 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 19:47:00.045 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_08_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 19:47:00.046 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:47:00.048 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_08_active_employees 无字段偏好设置，显示所有字段
2025-06-27 19:47:00.058 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_08_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 19:47:00.067 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 19:47:00.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 19:47:00.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 19:47:00.132 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 28 列
2025-06-27 19:47:00.195 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 104.65ms
2025-06-27 19:47:00.237 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 19:47:00.237 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 19:48:01.245 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 8月', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 19:48:01.245 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 8月
2025-06-27 19:48:01.245 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2944826477456 已经注册，将覆盖现有注册
2025-06-27 19:48:01.245 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2944826223088 已经注册，将覆盖现有注册
2025-06-27 19:48:01.245 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 19:48:01.245 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 19:48:01.245 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 19:48:01.245 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 19:48:01.245 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 19:48:01.264 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 19:48:01.278 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 19:48:01.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 93.73ms
2025-06-27 19:48:01.341 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 19:48:01.342 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:48:01.344 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 19:48:01.346 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 19:48:01.346 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 19:48:01.366 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 22.86ms
2025-06-27 19:48:01.380 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1101 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 19:48:01.429 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:48:01.433 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 8月
2025-06-27 19:48:05.251 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 8月', '工资表 > 2027年 > 8月 > A岗职工', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 19:48:05.251 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 8月 > A岗职工
2025-06-27 19:48:05.251 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2944826477456 已经注册，将覆盖现有注册
2025-06-27 19:48:05.251 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2944826223088 已经注册，将覆盖现有注册
2025-06-27 19:48:05.251 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 2 个表格到表头管理器
2025-06-27 19:48:05.251 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 19:48:05.251 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 19:48:05.251 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 19:48:05.251 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:48:05.251 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 19:48:05.251 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_08_a_grade_employees，第1页，每页50条
2025-06-27 19:48:05.251 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_08_a_grade_employees 第1页
2025-06-27 19:48:05.251 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 8月 > A岗职工
2025-06-27 19:48:05.251 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_08_a_grade_employees 第1页数据，每页50条
2025-06-27 19:48:05.251 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_08_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 19:48:05.296 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_08_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 19:48:05.313 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 19:48:05.319 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_08_a_grade_employees 没有字段映射配置
2025-06-27 19:48:05.323 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，26 个字段，总记录数: 62
2025-06-27 19:48:05.342 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 19:48:05.357 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_08_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 19:48:05.358 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:48:05.361 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_08_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 19:48:05.361 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 19:48:05.378 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 19:48:05.381 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_08_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 19:48:05.381 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 19:48:05.435 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 26 列
2025-06-27 19:48:05.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 104.36ms
2025-06-27 19:48:05.471 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 19:48:05.471 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 19:48:09.854 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-27 19:48:09.854 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2455 | 接收到数据导入请求，推断的目标路径: 工资表 > 2027年 > 8月 > A岗职工。打开导入对话框。
2025-06-27 19:48:09.854 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-27 19:48:09.870 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:48:09.870 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:70 | 多Sheet导入器初始化完成
2025-06-27 19:48:09.870 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-06-27 19:48:09.885 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2027年 > 8月 > A岗职工
2025-06-27 19:48:10.566 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:775 | 找到 123 个匹配类型 'salary_data' 的表
2025-06-27 19:48:10.667 | INFO     | src.gui.dialogs:_get_template_fields:1884 | 使用字段模板: 全部在职人员工资表
2025-06-27 19:48:10.839 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-27 19:48:10.840 | INFO     | src.gui.dialogs:_apply_default_settings:2206 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-06-27 19:48:10.841 | INFO     | src.gui.dialogs:_setup_tooltips:2461 | 工具提示设置完成
2025-06-27 19:48:10.842 | INFO     | src.gui.dialogs:_setup_shortcuts:2500 | 快捷键设置完成
2025-06-27 19:48:10.843 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-27 19:48:14.739 | INFO     | src.gui.dialogs:_on_target_changed:2145 | 目标位置已更新: 工资表 > 2027年 > 9月 > A岗职工
2025-06-27 19:48:19.215 | INFO     | src.gui.dialogs:_on_target_changed:2145 | 目标位置已更新: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:48:28.055 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 19:48:30.068 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 19:48:30.071 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-06-27 19:48:30.072 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2241 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-06-27 19:48:51.343 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 19:48:51.589 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 19:48:51.630 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 19:48:51.673 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:200 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 19:48:51.674 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 19:48:51.896 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 19:48:51.896 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 19:48:51.896 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 19:48:51.896 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 19:48:51.896 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 19:48:52.085 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-27 19:48:52.130 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 19:48:52.130 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-27 19:48:52.224 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-27 19:48:52.224 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-27 19:48:52.224 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 19:48:52.224 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1267 | 模板适配完成: 16 个字段
2025-06-27 19:48:52.224 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:474 | 使用 离休人员工资表 模板生成字段映射
2025-06-27 19:48:52.239 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_09_retired_employees
2025-06-27 19:48:52.240 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2027_09_retired_employees 生成标准化字段映射: 16 个字段
2025-06-27 19:48:52.241 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-27 19:48:52.249 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-27 19:48:52.261 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '离休人员工资表' 检测到模板类型: salary_data
2025-06-27 19:48:52.279 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_table:277 | 工资数据表创建成功: salary_data_2027_09
2025-06-27 19:48:52.295 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:913 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-27 19:48:52.314 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1024 | 成功向表 salary_data_2027_09 保存 2 条数据。
2025-06-27 19:48:52.320 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 19:48:52.321 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 19:48:52.322 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 19:48:52.488 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-27 19:48:52.537 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 19:48:52.538 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-27 19:48:52.538 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-27 19:48:52.538 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-27 19:48:52.538 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 19:48:52.538 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-06-27 19:48:52.538 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-06-27 19:48:52.538 | ERROR    | src.modules.data_import.config_sync_manager:_save_config_file:709 | 保存配置文件失败: [WinError 5] 拒绝访问。: 'state/data/field_mappings.json.tmp' -> 'state/data/field_mappings.json'
2025-06-27 19:48:52.538 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2027_09_pension_employees 生成标准化字段映射: 32 个字段
2025-06-27 19:48:52.550 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-27 19:48:52.563 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-27 19:48:52.564 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-06-27 19:48:52.583 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:348 | 专用工资数据表创建成功: salary_data_2027_09_pension_employees (模板: pension_employees)
2025-06-27 19:48:52.633 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:913 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 19:48:52.654 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1024 | 成功向表 salary_data_2027_09_pension_employees 保存 13 条数据。
2025-06-27 19:48:52.655 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 19:48:52.656 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 19:48:52.661 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 19:48:52.859 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-27 19:48:52.898 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 19:48:52.899 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-27 19:48:52.904 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-27 19:48:52.906 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-27 19:48:52.906 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-27 19:48:52.921 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-06-27 19:48:52.923 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-06-27 19:48:52.941 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_09_active_employees
2025-06-27 19:48:52.973 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2027_09_active_employees 生成标准化字段映射: 28 个字段
2025-06-27 19:48:52.976 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-27 19:48:52.989 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-27 19:48:52.999 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-06-27 19:48:53.009 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:348 | 专用工资数据表创建成功: salary_data_2027_09_active_employees (模板: active_employees)
2025-06-27 19:48:53.009 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:913 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 19:48:53.081 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1024 | 成功向表 salary_data_2027_09_active_employees 保存 1396 条数据。
2025-06-27 19:48:53.294 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 19:48:53.294 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 19:48:53.294 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 19:48:53.482 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-27 19:48:53.535 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 19:48:53.535 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-27 19:48:53.535 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-27 19:48:53.535 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:445 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-27 19:48:53.535 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:726 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-27 19:48:53.535 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-06-27 19:48:53.535 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:462 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-06-27 19:48:53.546 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_09_a_grade_employees
2025-06-27 19:48:53.547 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:504 | 为表 salary_data_2027_09_a_grade_employees 生成标准化字段映射: 26 个字段
2025-06-27 19:48:53.547 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:514 | Sheet A岗职工 存在 2 个验证错误
2025-06-27 19:48:53.547 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | Sheet A岗职工 数据处理完成: 62 行
2025-06-27 19:48:53.547 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:360 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-06-27 19:48:53.566 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:348 | 专用工资数据表创建成功: salary_data_2027_09_a_grade_employees (模板: a_grade_employees)
2025-06-27 19:48:53.598 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:913 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 19:48:53.648 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1024 | 成功向表 salary_data_2027_09_a_grade_employees 保存 62 条数据。
2025-06-27 19:48:53.659 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:230 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09 保存 2 条数据。', 'table_name': 'salary_data_2027_09', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_09_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_09_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_09_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_09_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-27 19:48:53.662 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09 保存 2 条数据。', 'table_name': 'salary_data_2027_09', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_09_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_09_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_09_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_09_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-09', 'data_description': '2027年9月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 9月 > 全部在职人员'}
2025-06-27 19:48:53.694 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2468 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09 保存 2 条数据。', 'table_name': 'salary_data_2027_09', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_09_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_09_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_09_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_09_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_09_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-09', 'data_description': '2027年9月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 9月 > 全部在职人员'}
2025-06-27 19:48:53.726 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2479 | 导入模式: multi_sheet, 目标路径: '工资表 > 2027年 > 9月 > 全部在职人员'
2025-06-27 19:48:53.728 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2487 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:48:53.729 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2545 | 检查是否需要更新导航面板: ['工资表', '2027年', '9月', '全部在职人员']
2025-06-27 19:48:53.729 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2549 | 检测到工资数据导入，开始刷新导航面板
2025-06-27 19:48:53.729 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2553 | 使用强制刷新方法
2025-06-27 19:48:53.731 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 19:48:53.731 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 19:48:53.731 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 8月', '工资表 > 2027年 > 8月 > A岗职工', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 19:48:53.731 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 8月 > A岗职工
2025-06-27 19:48:53.741 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2944826477456 已经注册，将覆盖现有注册
2025-06-27 19:48:53.744 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2944826223088 已经注册，将覆盖现有注册
2025-06-27 19:48:53.745 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 19:48:53.748 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 19:48:53.749 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.04ms
2025-06-27 19:48:53.750 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 19:48:53.751 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:48:53.752 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 19:48:53.756 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_08_a_grade_employees，第1页，每页50条
2025-06-27 19:48:53.757 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_08_a_grade_employees 第1页
2025-06-27 19:48:53.758 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_08_a_grade_employees 没有字段映射配置
2025-06-27 19:48:53.776 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-27 19:48:53.785 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:48:53.792 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_08_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 19:48:53.825 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 19:48:53.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 19:48:53.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 19:48:53.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 26 列
2025-06-27 19:48:53.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 57.91ms
2025-06-27 19:48:53.932 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 19:48:53.934 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 19:48:53.936 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 8月 > A岗职工
2025-06-27 19:48:54.015 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:775 | 找到 127 个匹配类型 'salary_data' 的表
2025-06-27 19:48:54.045 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1301 | 元数据项 salary_data_2027_06 缺少年份或月份信息，已跳过
2025-06-27 19:48:54.046 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1301 | 元数据项 salary_data_2027_08 缺少年份或月份信息，已跳过
2025-06-27 19:48:54.047 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1301 | 元数据项 salary_data_2027_09 缺少年份或月份信息，已跳过
2025-06-27 19:48:54.047 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 7 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 31 个月份
2025-06-27 19:48:54.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 19:48:54.060 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 19:48:54.065 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2558 | 将在800ms后导航到: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:48:54.878 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2591 | 尝试导航到新导入的路径: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:48:54.889 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 8月', '工资表 > 2027年 > 8月 > A岗职工', '工资表 > 2027年 > 9月 > 全部在职人员', '工资表 > 2027年', '工资表']
2025-06-27 19:48:54.891 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:48:54.898 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2944826477456 已经注册，将覆盖现有注册
2025-06-27 19:48:54.899 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2944826223088 已经注册，将覆盖现有注册
2025-06-27 19:48:54.900 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2945086539680 已经注册，将覆盖现有注册
2025-06-27 19:48:54.900 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2945086539104 已经注册，将覆盖现有注册
2025-06-27 19:48:54.900 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 19:48:54.901 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 19:48:54.902 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.00ms
2025-06-27 19:48:54.903 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 19:48:54.904 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:48:54.905 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 19:48:54.906 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_09_active_employees，第1页，每页50条
2025-06-27 19:48:54.906 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_09_active_employees 第1页
2025-06-27 19:48:54.907 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:48:54.910 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_09_active_employees 第1页数据，每页50条
2025-06-27 19:48:54.911 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2596 | 已成功导航到新导入的路径: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:48:54.912 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_09_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 19:48:54.936 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_09_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 19:48:54.994 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 19:48:54.996 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 28 个字段重命名
2025-06-27 19:48:54.998 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，28 个字段，总记录数: 1396
2025-06-27 19:48:54.999 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 19:48:55.021 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_09_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 19:48:55.022 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:48:55.024 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_09_active_employees 无字段偏好设置，显示所有字段
2025-06-27 19:48:55.024 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 19:48:55.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 19:48:55.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 19:48:55.048 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_09_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 19:48:55.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 28 个表头
2025-06-27 19:48:55.065 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 28 列
2025-06-27 19:48:55.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 109.28ms
2025-06-27 19:48:55.182 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 19:48:55.182 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 19:49:59.101 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 8月', '工资表 > 2027年 > 9月 > 全部在职人员', '工资表 > 2027年 > 8月 > A岗职工', '工资表 > 2027年', '工资表 > 2027年 > 9月 > A岗职工']
2025-06-27 19:49:59.101 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 9月 > A岗职工
2025-06-27 19:49:59.101 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2944826477456 已经注册，将覆盖现有注册
2025-06-27 19:49:59.101 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2944826223088 已经注册，将覆盖现有注册
2025-06-27 19:49:59.101 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2945086539680 已经注册，将覆盖现有注册
2025-06-27 19:49:59.101 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2945086539104 已经注册，将覆盖现有注册
2025-06-27 19:49:59.101 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 19:49:59.101 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 19:49:59.101 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 19:49:59.101 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 19:49:59.101 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:49:59.101 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(62条)，启用分页模式
2025-06-27 19:49:59.117 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_09_a_grade_employees，第1页，每页50条
2025-06-27 19:49:59.117 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2860 | 缓存未命中，从数据库加载: salary_data_2027_09_a_grade_employees 第1页
2025-06-27 19:49:59.117 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 9月 > A岗职工
2025-06-27 19:49:59.117 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_09_a_grade_employees 第1页数据，每页50条
2025-06-27 19:49:59.117 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_09_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 19:49:59.117 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_09_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 19:49:59.132 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 19:49:59.134 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2678 | 字段映射应用成功: 26 个字段重命名
2025-06-27 19:49:59.138 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，26 个字段，总记录数: 62
2025-06-27 19:49:59.167 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 19:49:59.209 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_09_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 19:49:59.211 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:49:59.213 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_09_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 19:49:59.213 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 19:49:59.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 19:49:59.245 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_09_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 19:49:59.329 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 19:49:59.360 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 26 个表头
2025-06-27 19:49:59.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 26 列
2025-06-27 19:49:59.475 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 251.81ms
2025-06-27 19:49:59.478 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 19:49:59.478 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 19:50:15.233 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 8月', '工资表 > 2027年 > 9月 > 全部在职人员', '工资表 > 2027年 > 8月 > A岗职工', '工资表 > 2027年', '工资表 > 2027年 > 9月 > 退休人员']
2025-06-27 19:50:15.233 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 9月 > 退休人员
2025-06-27 19:50:15.233 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2944826477456 已经注册，将覆盖现有注册
2025-06-27 19:50:15.233 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2944826223088 已经注册，将覆盖现有注册
2025-06-27 19:50:15.249 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2945086539680 已经注册，将覆盖现有注册
2025-06-27 19:50:15.249 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2945086539104 已经注册，将覆盖现有注册
2025-06-27 19:50:15.249 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 19:50:15.249 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 19:50:15.249 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 19:50:15.249 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 19:50:15.249 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:50:15.249 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2789 | 数据量小(13条)，使用普通模式
2025-06-27 19:50:15.249 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 9月 > 退休人员
2025-06-27 19:50:15.249 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:1057 | 正在从表 salary_data_2027_09_pension_employees 获取数据...
2025-06-27 19:50:15.249 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:1068 | 成功从表 salary_data_2027_09_pension_employees 获取 13 行数据。
2025-06-27 19:50:15.249 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2665 | 表 salary_data_2027_09_pension_employees 没有字段映射配置
2025-06-27 19:50:15.249 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:50:15.249 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_09_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 19:50:15.264 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 19:50:15.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 19:50:15.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 19:50:15.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 32 列
2025-06-27 19:50:15.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 84.99ms
2025-06-27 19:50:15.361 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 19:50:29.006 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 9月 > 全部在职人员', '工资表 > 2027年 > 8月', '工资表 > 2027年 > 8月 > A岗职工', '工资表 > 2027年', '工资表 > 2027年 > 9月 > 退休人员']
2025-06-27 19:50:29.006 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2728 | 导航变化: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:50:29.006 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2944826477456 已经注册，将覆盖现有注册
2025-06-27 19:50:29.006 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2944826223088 已经注册，将覆盖现有注册
2025-06-27 19:50:29.006 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2945086539680 已经注册，将覆盖现有注册
2025-06-27 19:50:29.006 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2945086539104 已经注册，将覆盖现有注册
2025-06-27 19:50:29.006 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3310 | 已注册 4 个表格到表头管理器
2025-06-27 19:50:29.006 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 19:50:29.006 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 19:50:29.006 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 19:50:29.006 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 19:50:29.006 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2786 | 数据量大(1396条)，启用分页模式
2025-06-27 19:50:29.006 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2817 | 使用分页模式加载 salary_data_2027_09_active_employees，第1页，每页50条
2025-06-27 19:50:29.022 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2826 | 缓存命中: salary_data_2027_09_active_employees 第1页
2025-06-27 19:50:29.022 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2682 | 表 salary_data_2027_09_active_employees 无需字段重命名
2025-06-27 19:50:29.022 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2890 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 19:50:29.022 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:50:29.022 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2718 | 表 salary_data_2027_09_active_employees 无字段偏好设置，显示所有字段
2025-06-27 19:50:29.022 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 19:50:29.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 50
2025-06-27 19:50:29.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 19:50:29.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 28 个表头
2025-06-27 19:50:29.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 28 列
2025-06-27 19:50:29.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 121.36ms
2025-06-27 19:50:29.225 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 19:50:29.226 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 19:50:29.240 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 9月 > 全部在职人员
2025-06-27 19:50:31.841 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 19:50:31.841 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:50:31.841 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 19:50:31.841 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_09_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 19:50:31.857 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_09_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 19:50:31.857 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 19:50:31.857 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 19:50:31.857 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 28 个表头
2025-06-27 19:50:31.857 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 28 列
2025-06-27 19:50:31.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 53.45ms
2025-06-27 19:50:31.942 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 28
2025-06-27 19:50:31.945 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 19:50:35.582 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 19:50:35.582 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 19:50:35.582 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 19:50:35.582 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1089 | 正在从表 salary_data_2027_09_active_employees 分页获取数据: 第3页, 每页50条
2025-06-27 19:50:35.582 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1112 | 成功从表 salary_data_2027_09_active_employees 获取第3页数据: 50 行，总计1396行
2025-06-27 19:50:35.598 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 19:50:35.598 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 19:50:35.598 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 28 个表头
2025-06-27 19:50:35.598 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 28 列
2025-06-27 19:50:35.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 58.37ms
2025-06-27 19:50:35.657 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第3页, 显示50条记录，字段数: 28
2025-06-27 19:50:35.666 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 3
2025-06-27 19:50:39.671 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-27 20:07:13.362 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 20:07:13.363 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 20:07:13.364 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 20:07:13.364 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 20:07:13.365 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 20:07:13.365 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 20:07:15.695 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 20:07:15.695 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 20:07:15.696 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 20:07:15.697 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 20:07:15.697 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 20:07:15.712 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 20:07:15.712 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 20:07:15.713 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-06-27 20:07:15.713 | INFO     | src.modules.data_storage.dynamic_table_manager:_parse_table_name:1430 | 表 salary_data_2027_09 使用默认员工类型: retired_employees
2025-06-27 20:07:15.718 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 20:07:15.718 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 20:07:15.719 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 20:07:15.719 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 20:07:15.721 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 20:07:15.729 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 20:07:15.747 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 20:07:15.748 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-06-27 20:07:16.551 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:775 | 找到 127 个匹配类型 'salary_data' 的表
2025-06-27 20:07:16.552 | INFO     | src.modules.data_storage.dynamic_table_manager:_parse_table_name:1430 | 表 salary_data_2027_06 使用默认员工类型: retired_employees
2025-06-27 20:07:16.553 | INFO     | src.modules.data_storage.dynamic_table_manager:_parse_table_name:1430 | 表 salary_data_2027_08 使用默认员工类型: retired_employees
2025-06-27 20:07:16.554 | INFO     | src.modules.data_storage.dynamic_table_manager:_parse_table_name:1430 | 表 salary_data_2027_09 使用默认员工类型: retired_employees
2025-06-27 20:07:16.557 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 20:07:16.560 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 20:07:16.561 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 20:07:16.572 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 20:07:16.572 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 20:07:16.572 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 20:07:16.584 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 20:07:16.584 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 20:07:16.584 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-06-27 20:07:16.641 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:775 | 找到 127 个匹配类型 'salary_data' 的表
2025-06-27 20:07:16.641 | INFO     | src.modules.data_storage.dynamic_table_manager:_parse_table_name:1430 | 表 salary_data_2027_06 使用默认员工类型: retired_employees
2025-06-27 20:07:16.641 | INFO     | src.modules.data_storage.dynamic_table_manager:_parse_table_name:1430 | 表 salary_data_2027_08 使用默认员工类型: retired_employees
2025-06-27 20:07:16.641 | INFO     | src.modules.data_storage.dynamic_table_manager:_parse_table_name:1430 | 表 salary_data_2027_09 使用默认员工类型: retired_employees
