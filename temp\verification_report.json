{"timestamp": "2025-06-27T17:57:08.237583", "verification_results": {"new_tables": ["salary_data_2027_06"], "table_structures": {"salary_data_2027_06": {"field_count": 49, "is_specialized": true, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at", "2019待遇调整", "人员代码", "序号", "公积", "增发一次性生活补贴", "2020待遇调整", "2016待遇调整", "人员类别代码", "离退休生活补贴", "离休补贴", "结余津贴", "import_time", "合计", "2018待遇调整", "基本离休费", "生活补贴", "姓名", "增资预付", "住房补贴", "2022待遇调整", "2021待遇调整", "部门名称", "data_source", "津贴", "物业补贴", "基本退休费", "2017待遇调整", "护理费", "应发工资", "补发", "保险扣款", "借支", "2023待遇调整"]}}, "field_mappings": {"salary_data_2027_06": {"mapping_exists": false, "mapping_count": 0}}, "data_samples": {}}, "summary": {"total_new_tables": 1, "specialized_tables": 1, "generic_tables": 0}}