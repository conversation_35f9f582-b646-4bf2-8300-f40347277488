#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段最终验证：数据完整性和功能测试
全面验证专用模板系统的功能和效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.system_config.specialized_table_templates import SpecializedTableTemplates
from src.modules.data_import.specialized_field_mapping_generator import SpecializedFieldMappingGenerator
from src.utils.log_config import setup_logger

def final_verification_and_testing():
    """最终验证和测试"""
    logger = setup_logger(__name__)
    
    print("=" * 80)
    print("第三阶段最终验证：数据完整性和功能测试")
    print("=" * 80)
    
    verification_results = {
        "timestamp": datetime.now().isoformat(),
        "tests": {},
        "summary": {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "warnings": 0
        }
    }
    
    # 1. 专用模板功能测试
    print("\n1. 专用模板功能测试")
    print("-" * 50)
    
    test_name = "specialized_templates"
    verification_results["tests"][test_name] = {}
    
    try:
        templates = SpecializedTableTemplates()
        
        # 测试模板信息
        template_info = templates.get_template_info()
        print(f"✓ 专用模板数量: {len(template_info)}")
        
        # 测试表头检测
        test_headers = {
            "离休人员工资表": ["序号", "人员代码", "姓名", "基本离休费", "护理费"],
            "退休人员工资表": ["序号", "人员代码", "姓名", "基本退休费", "2016待遇调整"],
            "全部在职人员工资表": ["序号", "工号", "姓名", "2025年岗位工资", "2025年薪级工资"],
            "A岗职工工资表": ["序号", "工号", "姓名", "2025年校龄工资", "2025年生活补贴"]
        }
        
        detection_results = {}
        for sheet_name, headers in test_headers.items():
            template_key = templates.detect_template_by_headers(headers)
            detection_results[sheet_name] = template_key
            print(f"  {sheet_name}: {template_key}")
        
        verification_results["tests"][test_name] = {
            "status": "passed",
            "template_count": len(template_info),
            "detection_results": detection_results
        }
        verification_results["summary"]["passed_tests"] += 1
        
    except Exception as e:
        print(f"✗ 专用模板功能测试失败: {e}")
        verification_results["tests"][test_name] = {"status": "failed", "error": str(e)}
        verification_results["summary"]["failed_tests"] += 1
    
    verification_results["summary"]["total_tests"] += 1
    
    # 2. 字段映射生成测试
    print("\n2. 字段映射生成测试")
    print("-" * 50)
    
    test_name = "field_mapping_generation"
    
    try:
        mapping_generator = SpecializedFieldMappingGenerator()
        
        mapping_results = {}
        for sheet_name, headers in test_headers.items():
            template_key = templates.detect_template_by_headers(headers)
            mapping = mapping_generator.generate_mapping(headers, template_key)
            mapping_results[sheet_name] = {
                "template_key": template_key,
                "mapping_count": len(mapping),
                "sample_mappings": dict(list(mapping.items())[:3])
            }
            print(f"  {sheet_name}: {len(mapping)} 个字段映射")
        
        verification_results["tests"][test_name] = {
            "status": "passed",
            "mapping_results": mapping_results
        }
        verification_results["summary"]["passed_tests"] += 1
        
    except Exception as e:
        print(f"✗ 字段映射生成测试失败: {e}")
        verification_results["tests"][test_name] = {"status": "failed", "error": str(e)}
        verification_results["summary"]["failed_tests"] += 1
    
    verification_results["summary"]["total_tests"] += 1
    
    # 3. 数据库表结构对比
    print("\n3. 数据库表结构对比")
    print("-" * 50)
    
    test_name = "table_structure_comparison"
    
    try:
        table_manager = DynamicTableManager()
        
        # 对比新旧表结构
        old_tables = [
            "salary_data_2027_05_active_employees",
            "salary_data_2027_05_retired_employees",
            "salary_data_2027_05_pension_employees",
            "salary_data_2027_05_a_grade_employees"
        ]
        
        new_tables = ["salary_data_2027_06"]
        
        comparison_results = {}
        
        # 检查旧表结构
        for table_name in old_tables:
            if table_manager.table_exists(table_name):
                columns = table_manager.get_table_columns(table_name)
                comparison_results[table_name] = {
                    "type": "old_generic",
                    "field_count": len(columns),
                    "fields": [col.get('name', '') for col in columns]
                }
                print(f"  旧表 {table_name}: {len(columns)} 个字段")
        
        # 检查新表结构
        for table_name in new_tables:
            if table_manager.table_exists(table_name):
                columns = table_manager.get_table_columns(table_name)
                comparison_results[table_name] = {
                    "type": "new_specialized",
                    "field_count": len(columns),
                    "fields": [col.get('name', '') for col in columns]
                }
                print(f"  新表 {table_name}: {len(columns)} 个字段")
        
        # 计算改进效果
        old_field_count = 16  # 通用模板字段数
        new_field_count = max([r["field_count"] for r in comparison_results.values() 
                              if r["type"] == "new_specialized"], default=0)
        
        if new_field_count > old_field_count:
            improvement = new_field_count - old_field_count
            improvement_percent = (improvement / old_field_count) * 100
            print(f"  ✓ 字段数量提升: +{improvement} 个字段 (+{improvement_percent:.1f}%)")
        
        verification_results["tests"][test_name] = {
            "status": "passed",
            "comparison_results": comparison_results,
            "improvement": {
                "old_field_count": old_field_count,
                "new_field_count": new_field_count,
                "improvement_count": new_field_count - old_field_count,
                "improvement_percent": ((new_field_count - old_field_count) / old_field_count) * 100
            }
        }
        verification_results["summary"]["passed_tests"] += 1
        
    except Exception as e:
        print(f"✗ 数据库表结构对比失败: {e}")
        verification_results["tests"][test_name] = {"status": "failed", "error": str(e)}
        verification_results["summary"]["failed_tests"] += 1
    
    verification_results["summary"]["total_tests"] += 1
    
    # 4. 数据完整性验证
    print("\n4. 数据完整性验证")
    print("-" * 50)
    
    test_name = "data_integrity"
    
    try:
        db_path = Path("data/db/salary_system.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查数据记录
        data_integrity_results = {}
        
        for table_name in new_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_count = cursor.fetchone()[0]
            
            # 检查关键字段的数据完整性
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE employee_id IS NOT NULL AND employee_name IS NOT NULL")
            valid_count = cursor.fetchone()[0]
            
            data_integrity_results[table_name] = {
                "total_records": total_count,
                "valid_records": valid_count,
                "integrity_rate": (valid_count / total_count) * 100 if total_count > 0 else 0
            }
            
            print(f"  表 {table_name}:")
            print(f"    总记录数: {total_count}")
            print(f"    有效记录数: {valid_count}")
            print(f"    完整性率: {data_integrity_results[table_name]['integrity_rate']:.1f}%")
        
        conn.close()
        
        verification_results["tests"][test_name] = {
            "status": "passed",
            "data_integrity_results": data_integrity_results
        }
        verification_results["summary"]["passed_tests"] += 1
        
    except Exception as e:
        print(f"✗ 数据完整性验证失败: {e}")
        verification_results["tests"][test_name] = {"status": "failed", "error": str(e)}
        verification_results["summary"]["failed_tests"] += 1
    
    verification_results["summary"]["total_tests"] += 1
    
    # 5. 系统功能集成测试
    print("\n5. 系统功能集成测试")
    print("-" * 50)
    
    test_name = "system_integration"
    
    try:
        # 测试配置同步管理器
        config_sync = ConfigSyncManager()
        
        # 为新表生成字段映射配置
        integration_results = {}
        
        for table_name in new_tables:
            if table_manager.table_exists(table_name):
                columns = table_manager.get_table_columns(table_name)
                
                # 生成测试映射
                test_mapping = {}
                for col in columns[:10]:  # 只测试前10个字段
                    field_name = col.get('name', '')
                    if field_name:
                        test_mapping[field_name] = field_name.replace('_', ' ').title()
                
                # 保存映射配置
                success = config_sync.save_mapping(f"{table_name}_test", test_mapping)
                
                integration_results[table_name] = {
                    "mapping_save_success": success,
                    "test_mapping_count": len(test_mapping)
                }
                
                print(f"  表 {table_name}: 映射配置保存 {'成功' if success else '失败'}")
        
        verification_results["tests"][test_name] = {
            "status": "passed",
            "integration_results": integration_results
        }
        verification_results["summary"]["passed_tests"] += 1
        
    except Exception as e:
        print(f"✗ 系统功能集成测试失败: {e}")
        verification_results["tests"][test_name] = {"status": "failed", "error": str(e)}
        verification_results["summary"]["failed_tests"] += 1
    
    verification_results["summary"]["total_tests"] += 1
    
    # 6. 生成最终报告
    print("\n6. 生成最终验证报告")
    print("-" * 50)
    
    try:
        # 计算总体成功率
        total_tests = verification_results["summary"]["total_tests"]
        passed_tests = verification_results["summary"]["passed_tests"]
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        verification_results["summary"]["success_rate"] = success_rate
        
        # 保存详细报告
        report_file = Path("temp/final_verification_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(verification_results, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 最终验证报告保存成功: {report_file}")
        print(f"\n验证总结:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  失败测试: {verification_results['summary']['failed_tests']}")
        print(f"  成功率: {success_rate:.1f}%")
        
        # 生成简化的成功报告
        if success_rate >= 80:
            print(f"\n🎉 验证成功! 专用模板系统运行正常")
            return True
        else:
            print(f"\n⚠ 验证部分成功，需要进一步优化")
            return False
        
    except Exception as e:
        print(f"✗ 生成最终报告失败: {e}")
        return False

if __name__ == "__main__":
    success = final_verification_and_testing()
    if success:
        print(f"\n✅ 第三阶段验证完成!")
    else:
        print(f"\n❌ 第三阶段验证需要改进!")
