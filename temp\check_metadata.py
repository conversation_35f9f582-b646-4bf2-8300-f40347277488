#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json

def main():
    # 连接数据库
    conn = sqlite3.connect('data/db/salary_system.db')
    cursor = conn.cursor()

    # 查看table_metadata表的结构
    print('=== table_metadata表结构 ===')
    cursor.execute('PRAGMA table_info(table_metadata)')
    columns = cursor.fetchall()
    for col in columns:
        print(f'{col[1]} ({col[2]})')

    print('\n=== 2027年9月相关的元数据 ===')
    cursor.execute('SELECT * FROM table_metadata WHERE table_name LIKE "%2027_09%"')
    rows = cursor.fetchall()
    for row in rows:
        print(f'表名: {row[1]}')
        print(f'  类型: {row[2]}')
        print(f'  显示名: {row[3]}')
        print(f'  年份: {row[4]}')
        print(f'  月份: {row[5]}')
        print(f'  创建时间: {row[6]}')
        print('---')

    print('\n=== 所有2027年相关表的元数据 ===')
    cursor.execute('SELECT * FROM table_metadata WHERE table_name LIKE "%2027%" ORDER BY table_name')
    rows = cursor.fetchall()
    for row in rows:
        print(f'ID: {row[0]}')
        print(f'表名: {row[1]}')
        print(f'类型: {row[2]}')
        print(f'模式版本: {row[3]}')
        print(f'描述: {row[4]}')
        print(f'创建时间: {row[5]}')
        print(f'更新时间: {row[6]}')
        print(f'是否活跃: {row[7]}')
        print('---')

    print('\n=== 检查是否有专门的年月字段表 ===')
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%metadata%'")
    metadata_tables = cursor.fetchall()
    for table in metadata_tables:
        print(f'元数据表: {table[0]}')
        cursor.execute(f'PRAGMA table_info({table[0]})')
        columns = cursor.fetchall()
        for col in columns:
            print(f'  {col[1]} ({col[2]})')

    conn.close()

if __name__ == "__main__":
    main()
